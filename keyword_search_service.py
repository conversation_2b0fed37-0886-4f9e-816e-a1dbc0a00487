"""
关键词搜索服务
提供高级搜索功能，包括多种搜索模式、聚合分析、统计等
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass

from loguru import logger
from pydantic import BaseModel, Field

from es_client import ElasticsearchClient, ESResponse
from keyword_index_manager import KeywordIndexManager


@dataclass
class SearchOptions:
    """搜索选项"""
    size: int = 10
    from_: int = 0
    highlight: bool = True
    include_aggregations: bool = False
    fuzzy: bool = False
    boost_exact_match: float = 3.0
    boost_partial_match: float = 2.0
    boost_fuzzy_match: float = 1.0


class SearchResult(BaseModel):
    """搜索结果模型"""
    total: int = Field(description="总结果数")
    took: int = Field(description="查询耗时(ms)")
    hits: List[Dict] = Field(description="命中结果")
    aggregations: Optional[Dict] = Field(default=None, description="聚合结果")
    suggestions: Optional[List[str]] = Field(default=None, description="搜索建议")


class KeywordSearchService:
    """关键词搜索服务"""
    
    def __init__(self, es_client: ElasticsearchClient, index_name: str = "keyword_documents"):
        self.es_client = es_client
        self.index_manager = KeywordIndexManager(es_client, index_name)
        self.index_name = index_name
        
    def search_keywords(
        self,
        keyword: str,
        options: SearchOptions = SearchOptions()
    ) -> SearchResult:
        """搜索关键词"""
        logger.info(f"执行关键词搜索: {keyword}")
        
        # 构建查询
        query = self._build_keyword_query(keyword, options)
        
        # 构建聚合
        aggs = None
        if options.include_aggregations:
            aggs = self._build_aggregations()
        
        # 构建高亮
        highlight = None
        if options.highlight:
            highlight = self._build_highlight()
        
        # 执行搜索
        if aggs:
            result = self.es_client.aggregate(
                index_name=self.index_name,
                aggs=aggs,
                query=query,
                size=options.size
            )
        else:
            result = self.es_client.search(
                index_name=self.index_name,
                query=query,
                size=options.size,
                from_=options.from_,
                highlight=highlight,
                sort=[
                    {"_score": {"order": "desc"}},
                    {"keyword_count": {"order": "desc"}}
                ]
            )
        
        if result.success:
            # 处理搜索结果
            search_result = self._process_search_result(result, options)
            logger.info(f"关键词搜索完成，找到 {search_result.total} 条结果")
            return search_result
        else:
            logger.error(f"关键词搜索失败: {result.error}")
            return SearchResult(total=0, took=0, hits=[])
    
    def multi_keyword_search(
        self,
        keywords: List[str],
        operator: str = "OR",
        options: SearchOptions = SearchOptions()
    ) -> SearchResult:
        """多关键词搜索"""
        logger.info(f"执行多关键词搜索: {keywords}, 操作符: {operator}")
        
        if not keywords:
            return SearchResult(total=0, took=0, hits=[])
        
        # 构建多关键词查询
        if operator.upper() == "AND":
            query = {
                "bool": {
                    "must": [
                        self._build_single_keyword_query(kw, options) 
                        for kw in keywords
                    ]
                }
            }
        else:  # OR
            query = {
                "bool": {
                    "should": [
                        self._build_single_keyword_query(kw, options) 
                        for kw in keywords
                    ],
                    "minimum_should_match": 1
                }
            }
        
        # 执行搜索
        result = self.es_client.search(
            index_name=self.index_name,
            query=query,
            size=options.size,
            from_=options.from_,
            highlight=self._build_highlight() if options.highlight else None,
            sort=[
                {"_score": {"order": "desc"}},
                {"keyword_count": {"order": "desc"}}
            ]
        )
        
        if result.success:
            search_result = self._process_search_result(result, options)
            logger.info(f"多关键词搜索完成，找到 {search_result.total} 条结果")
            return search_result
        else:
            logger.error(f"多关键词搜索失败: {result.error}")
            return SearchResult(total=0, took=0, hits=[])
    
    def search_by_chunk_pattern(
        self,
        pattern: str,
        options: SearchOptions = SearchOptions()
    ) -> SearchResult:
        """根据文档块ID模式搜索"""
        logger.info(f"执行文档块模式搜索: {pattern}")
        
        query = {
            "bool": {
                "should": [
                    {
                        "wildcard": {
                            "chunk_id": f"*{pattern}*"
                        }
                    },
                    {
                        "regexp": {
                            "chunk_id": f".*{pattern}.*"
                        }
                    }
                ]
            }
        }
        
        result = self.es_client.search(
            index_name=self.index_name,
            query=query,
            size=options.size,
            from_=options.from_,
            sort=[
                {"keyword_count": {"order": "desc"}},
                {"chunk_id": {"order": "asc"}}
            ]
        )
        
        if result.success:
            search_result = self._process_search_result(result, options)
            logger.info(f"文档块模式搜索完成，找到 {search_result.total} 条结果")
            return search_result
        else:
            logger.error(f"文档块模式搜索失败: {result.error}")
            return SearchResult(total=0, took=0, hits=[])
    
    def get_keyword_statistics(self) -> Dict[str, Any]:
        """获取关键词统计信息"""
        logger.info("获取关键词统计信息")
        
        aggs = {
            "total_documents": {
                "value_count": {
                    "field": "chunk_id"
                }
            },
            "avg_keyword_count": {
                "avg": {
                    "field": "keyword_count"
                }
            },
            "max_keyword_count": {
                "max": {
                    "field": "keyword_count"
                }
            },
            "min_keyword_count": {
                "min": {
                    "field": "keyword_count"
                }
            },
            "keyword_count_distribution": {
                "histogram": {
                    "field": "keyword_count",
                    "interval": 5
                }
            },
            "top_keywords": {
                "terms": {
                    "field": "keywords.keyword",
                    "size": 20
                }
            }
        }
        
        result = self.es_client.aggregate(
            index_name=self.index_name,
            aggs=aggs,
            size=0
        )
        
        if result.success:
            stats = result.data.get('aggregations', {})
            logger.info("关键词统计信息获取成功")
            return {
                "total_documents": stats.get('total_documents', {}).get('value', 0),
                "avg_keyword_count": round(stats.get('avg_keyword_count', {}).get('value', 0), 2),
                "max_keyword_count": stats.get('max_keyword_count', {}).get('value', 0),
                "min_keyword_count": stats.get('min_keyword_count', {}).get('value', 0),
                "keyword_count_distribution": stats.get('keyword_count_distribution', {}).get('buckets', []),
                "top_keywords": [
                    {"keyword": bucket['key'], "count": bucket['doc_count']}
                    for bucket in stats.get('top_keywords', {}).get('buckets', [])
                ]
            }
        else:
            logger.error(f"获取关键词统计信息失败: {result.error}")
            return {}
    
    def get_similar_documents(
        self,
        chunk_id: str,
        size: int = 5
    ) -> SearchResult:
        """获取相似文档"""
        logger.info(f"获取相似文档: {chunk_id}")
        
        # 首先获取目标文档的关键词
        target_result = self.index_manager.search_by_chunk_id(chunk_id)
        if not target_result.success or not target_result.data['hits']['hits']:
            logger.warning(f"目标文档不存在: {chunk_id}")
            return SearchResult(total=0, took=0, hits=[])
        
        target_doc = target_result.data['hits']['hits'][0]['_source']
        target_keywords = target_doc['keywords']
        
        # 构建相似性查询
        query = {
            "bool": {
                "should": [
                    {
                        "terms": {
                            "keywords.keyword": target_keywords,
                            "boost": 2.0
                        }
                    }
                ],
                "must_not": [
                    {
                        "term": {
                            "chunk_id": chunk_id
                        }
                    }
                ]
            }
        }
        
        result = self.es_client.search(
            index_name=self.index_name,
            query=query,
            size=size,
            sort=[
                {"_score": {"order": "desc"}},
                {"keyword_count": {"order": "desc"}}
            ]
        )
        
        if result.success:
            search_result = self._process_search_result(result, SearchOptions())
            logger.info(f"相似文档搜索完成，找到 {search_result.total} 条结果")
            return search_result
        else:
            logger.error(f"相似文档搜索失败: {result.error}")
            return SearchResult(total=0, took=0, hits=[])
    
    def _build_keyword_query(self, keyword: str, options: SearchOptions) -> Dict:
        """构建关键词查询"""
        should_clauses = [
            # 精确匹配
            {
                "term": {
                    "keywords.keyword": {
                        "value": keyword,
                        "boost": options.boost_exact_match
                    }
                }
            },
            # 部分匹配
            {
                "match": {
                    "keywords": {
                        "query": keyword,
                        "boost": options.boost_partial_match
                    }
                }
            },
            # 通配符匹配
            {
                "wildcard": {
                    "keywords.keyword": {
                        "value": f"*{keyword}*",
                        "boost": options.boost_fuzzy_match
                    }
                }
            }
        ]
        
        # 模糊匹配
        if options.fuzzy:
            should_clauses.append({
                "fuzzy": {
                    "keywords": {
                        "value": keyword,
                        "fuzziness": "AUTO",
                        "boost": options.boost_fuzzy_match * 0.5
                    }
                }
            })
        
        return {
            "bool": {
                "should": should_clauses,
                "minimum_should_match": 1
            }
        }
    
    def _build_single_keyword_query(self, keyword: str, options: SearchOptions) -> Dict:
        """构建单个关键词查询"""
        return {
            "bool": {
                "should": [
                    {
                        "term": {
                            "keywords.keyword": {
                                "value": keyword,
                                "boost": 2.0
                            }
                        }
                    },
                    {
                        "match": {
                            "keywords": keyword
                        }
                    }
                ]
            }
        }
    
    def _build_aggregations(self) -> Dict:
        """构建聚合查询"""
        return {
            "keyword_count_stats": {
                "stats": {
                    "field": "keyword_count"
                }
            },
            "top_keywords": {
                "terms": {
                    "field": "keywords.keyword",
                    "size": 10
                }
            }
        }
    
    def _build_highlight(self) -> Dict:
        """构建高亮配置"""
        return {
            "fields": {
                "keywords": {
                    "pre_tags": ["<mark>"],
                    "post_tags": ["</mark>"],
                    "fragment_size": 100,
                    "number_of_fragments": 3
                }
            }
        }
    
    def _process_search_result(self, result: ESResponse, options: SearchOptions) -> SearchResult:
        """处理搜索结果"""
        data = result.data
        hits = data.get('hits', {})
        
        return SearchResult(
            total=result.total or hits.get('total', {}).get('value', 0),
            took=result.took or data.get('took', 0),
            hits=hits.get('hits', []),
            aggregations=data.get('aggregations') if options.include_aggregations else None
        )
