import json
import logging
from typing import List, Dict, Optional, Any
from elasticsearch import Elasticsearch
from elasticsearch.exceptions import (
    ConnectionError, RequestError, NotFoundError, ConflictError
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("ESClient")


class ESClient:
    """支持代理服务器的Elasticsearch客户端（兼容单机和集群模式）"""
    
    def __init__(
        self,
        hosts: Optional[List[str]] = None,
        single_host: Optional[str] = None,
        user: Optional[str] = None,
        password: Optional[str] = None,
        # 代理服务器配置
        proxy_url: Optional[str] = None,
        # 集群模式参数
        sniff_on_start: bool = False,
        sniff_on_connection_fail: bool = False,
        sniffer_timeout: int = 60,** kwargs
    ):
        """
        初始化支持代理的ES客户端
        
        :param proxy_url: 代理服务器地址（如 "http://proxy-host:port" 或 "socks5://proxy-host:port"）
        其他参数同之前的ESClient
        """
        # 校验模式参数
        if hosts and single_host:
            raise ValueError("只能指定 hosts（集群）或 single_host（单机）中的一个")
        if not hosts and not single_host:
            single_host = "http://localhost:9200"
        
        self.hosts = hosts if hosts else [single_host]
        self.user = user
        self.password = password
        self.proxy_url = proxy_url  # 新增代理配置
        self.client: Optional[Elasticsearch] = None
        self.is_cluster = bool(hosts)
        
        # 基础配置
        self.base_config = {
            "timeout": kwargs.pop("timeout", 30),
            "max_retries": kwargs.pop("max_retries", 3),
            "retry_on_timeout": kwargs.pop("retry_on_timeout", True),
        }
        self.base_config.update(kwargs)
        
        # 集群模式配置
        if self.is_cluster:
            self.base_config.update({
                "sniff_on_start": sniff_on_start,
                "sniff_on_connection_fail": sniff_on_connection_fail,
                "sniffer_timeout": sniffer_timeout
            })
        
        # 添加代理配置（关键改动）
        if self.proxy_url:
            self._configure_proxy()
        
        self.connect()

    def _configure_proxy(self):
        """配置代理服务器参数"""
        try:
            from urllib3 import ProxyManager, make_headers
            from elasticsearch import RequestsHttpConnection
        except ImportError as e:
            logger.error(f"启用代理需要安装依赖：{e}，请执行 pip install urllib3 requests")
            raise
        
        # 创建带代理的连接池
        proxy_headers = make_headers(proxy_basic_auth=self._get_proxy_auth()) if self._get_proxy_auth() else None
        
        # 根据代理类型创建连接管理器
        http_client = ProxyManager(
            self.proxy_url,
            headers=proxy_headers,
            num_pools=10,  # 连接池大小
            timeout=self.base_config["timeout"]
        )
        
        # 替换ES的默认HTTP连接
        self.base_config["connection_class"] = RequestsHttpConnection
        self.base_config["http_client"] = http_client

    def _get_proxy_auth(self) -> Optional[str]:
        """从代理URL中提取认证信息（如 user:password@host:port 格式）"""
        if not self.proxy_url:
            return None
        
        from urllib.parse import urlparse
        parsed = urlparse(self.proxy_url)
        if parsed.username and parsed.password:
            return f"{parsed.username}:{parsed.password}"
        return None

    def connect(self) -> bool:
        """建立连接（含代理支持）"""
        try:
            conn_params = {
                "hosts": self.hosts,
                **self.base_config
            }
            
            # 添加ES认证
            if self.user and self.password:
                conn_params["basic_auth"] = (self.user, self.password)
            
            self.client = Elasticsearch(** conn_params)
            
            if self.client.ping():
                info = self.client.info()
                if self.is_cluster:
                    logger.info(
                        f"通过代理 {self.proxy_url or '无'} 连接到ES集群 {info['cluster_name']}"
                    )
                else:
                    logger.info(
                        f"通过代理 {self.proxy_url or '无'} 连接到ES单机 {self.hosts[0]}"
                    )
                return True
            else:
                logger.error("连接失败：无法ping通节点")
                self.client = None
                return False
                
        except ConnectionError as e:
            logger.error(f"代理连接错误：{str(e)}")
            self.client = None
            return False
        except Exception as e:
            logger.error(f"初始化客户端失败：{str(e)}")
            self.client = None
            return False

    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.client is not None and self.client.ping()

    # ------------------------------ 索引操作 ------------------------------
    def create_index(
        self,
        index_name: str,
        mapping: Optional[Dict] = None,
        settings: Optional[Dict] = None
    ) -> bool:
        """创建索引（支持自定义映射和设置）"""
        if not self.is_connected():
            logger.error("创建索引失败：未连接到 ES")
            return False
        
        try:
            if self.client.indices.exists(index=index_name):
                logger.warning(f"索引 {index_name} 已存在")
                return True
            
            index_body = {}
            if settings:
                index_body["settings"] = settings
            if mapping:
                index_body["mappings"] = mapping
            
            self.client.indices.create(index=index_name, body=index_body)
            logger.info(f"索引 {index_name} 创建成功")
            return True
        except RequestError as e:
            logger.error(f"创建索引失败（请求错误）：{str(e)}")
            return False
        except Exception as e:
            logger.error(f"创建索引失败：{str(e)}")
            return False

    def delete_index(self, index_name: str) -> bool:
        """删除索引（支持通配符）"""
        if not self.is_connected():
            logger.error("删除索引失败：未连接到 ES")
            return False
        
        try:
            if not self.client.indices.exists(index=index_name):
                logger.warning(f"索引 {index_name} 不存在")
                return True
            
            self.client.indices.delete(index=index_name)
            logger.info(f"索引 {index_name} 删除成功")
            return True
        except NotFoundError:
            logger.warning(f"删除失败：索引 {index_name} 不存在")
            return True
        except Exception as e:
            logger.error(f"删除索引失败：{str(e)}")
            return False

    # ------------------------------ 文档操作 ------------------------------
    def insert_document(
        self,
        index_name: str,
        doc: Dict,
        doc_id: Optional[str] = None,
        refresh: bool = False
    ) -> Optional[str]:
        """插入单条文档"""
        if not self.is_connected():
            logger.error("插入文档失败：未连接到 ES")
            return None
        
        try:
            kwargs = {"index": index_name, "document": doc, "refresh": refresh}  # 注意：ES 8.x 用 document 替代 body
            if doc_id:
                kwargs["id"] = doc_id
            
            response = self.client.index(** kwargs)
            doc_id = response["_id"]
            logger.debug(f"文档 {doc_id} 插入索引 {index_name} 成功")
            return doc_id
        except ConflictError:
            logger.error(f"插入失败：文档 ID {doc_id} 已存在")
            return None
        except Exception as e:
            logger.error(f"插入文档失败：{str(e)}")
            return None

    def bulk_insert(
        self,
        index_name: str,
        docs: List[Dict],
        doc_ids: Optional[List[str]] = None,
        refresh: bool = False
    ) -> bool:
        """批量插入文档"""
        if len(docs) == 0:
            logger.warning("批量插入：文档列表为空")
            return True
        
        if doc_ids and len(doc_ids) != len(docs):
            logger.error("批量插入失败：doc_ids 与 docs 长度不一致")
            return False
        
        operations = []
        for i, doc in enumerate(docs):
            op = {"index": {"_index": index_name}}
            if doc_ids:
                op["index"]["_id"] = doc_ids[i]
            operations.append(op)
            operations.append(doc)
        
        return self.bulk_operation(operations, refresh=refresh)

    def bulk_operation(
        self,
        operations: List[Dict],
        refresh: bool = False
    ) -> bool:
        """批量执行操作（插入/更新/删除）"""
        if not self.is_connected():
            logger.error("批量操作失败：未连接到 ES")
            return False
        
        if not operations:
            logger.warning("批量操作：操作列表为空")
            return True
        
        try:
            from elasticsearch.helpers import bulk
            
            success, failed = bulk(
                self.client,
                operations,
                refresh=refresh,
                raise_on_error=False
            )
            
            if failed:
                logger.error(f"批量操作部分失败：成功 {success} 条，失败 {len(failed)} 条")
                for err in failed:
                    logger.error(f"失败详情：{json.dumps(err, ensure_ascii=False)}")
                return False
            else:
                logger.info(f"批量操作成功：共处理 {success} 条")
                return True
        except Exception as e:
            logger.error(f"批量操作失败：{str(e)}")
            return False

    # ------------------------------ 查询操作 ------------------------------
    def search(
        self,
        index_name: str,
        query: Dict,
        size: int = 10,
        from_: int = 0,
        sort: Optional[List[Dict]] = None,
        source: Optional[List[str]] = None
    ) -> Optional[Dict]:
        """执行查询（支持分页、排序、字段过滤）"""
        if not self.is_connected():
            logger.error("查询失败：未连接到 ES")
            return None
        
        try:
            body = {"query": query}
            if sort:
                body["sort"] = sort
            if source is not None:
                body["_source"] = source
            
            response = self.client.search(
                index=index_name,
                body=body,
                size=size,
                from_=from_
            )
            logger.debug(f"查询 {index_name} 成功，命中 {response['hits']['total']['value']} 条")
            return response
        except NotFoundError:
            logger.warning(f"查询失败：索引 {index_name} 不存在")
            return None
        except Exception as e:
            logger.error(f"查询失败：{str(e)}")
            return None

    def count(self, index_name: str, query: Dict) -> int:
        """统计文档数量"""
        if not self.is_connected():
            logger.error("计数失败：未连接到 ES")
            return -1
        
        try:
            response = self.client.count(index=index_name, body={"query": query})
            return response["count"]
        except NotFoundError:
            logger.warning(f"计数失败：索引 {index_name} 不存在")
            return 0
        except Exception as e:
            logger.error(f"计数失败：{str(e)}")
            return -1

    # ------------------------------ 集群信息（集群模式专用） ------------------------------
    def get_cluster_health(self) -> Optional[Dict]:
        """获取集群健康状态（仅集群模式有效）"""
        if not self.is_connected():
            logger.error("获取集群健康失败：未连接到 ES")
            return None
        if not self.is_cluster:
            logger.warning("get_cluster_health 仅适用于集群模式")
            return None
        
        try:
            return self.client.cluster.health()
        except Exception as e:
            logger.error(f"获取集群健康失败：{str(e)}")
            return None

    def get_cluster_nodes(self) -> Optional[Dict]:
        """获取集群节点信息（仅集群模式有效）"""
        if not self.is_connected():
            logger.error("获取节点信息失败：未连接到 ES")
            return None
        if not self.is_cluster:
            logger.warning("get_cluster_nodes 仅适用于集群模式")
            return None
        
        try:
            return self.client.nodes.info()
        except Exception as e:
            logger.error(f"获取节点信息失败：{str(e)}")
            return None

    # ------------------------------ 其他操作 ------------------------------
    def close(self):
        """关闭连接"""
        if self.client:
            self.client.close()
            logger.info("ES 客户端已关闭")


# ------------------------------ 使用示例 ------------------------------
if __name__ == "__main__":
    # 1. 通过HTTP代理连接
    print("--- HTTP代理模式 ---")
    es_http_proxy = ESClient(
        hosts=[
            "http://*************:9200",
            "http://*************:9200",
            "http://*************:9200"
        ],
        user="elastic",
        password="RBktQ%a2*NeV%By",
        proxy_url="socks5://*************:44444",  # HTTP代理（带认证）
        sniff_on_start=True
    )
    if es_http_proxy.is_connected():
        print("HTTP代理连接成功")
    es_http_proxy.close()


    # 3. 无代理连接（兼容原有模式）
    print("\n--- 无代理模式 ---")
    es_no_proxy = ESClient(
       hosts=[
            "http://*************:9200",
            "http://*************:9200",
            "http://*************:9200"
        ],
        user="elastic",
        password="RBktQ%a2*NeV%By"
    )
    if es_no_proxy.is_connected():
        print("无代理连接成功")
    es_no_proxy.close()