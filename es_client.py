"""
Elasticsearch客户端模块
支持单机、集群模式，代理访问，完整的增删改查、批量操作、状态查询、日志记录等功能
"""

import json
import asyncio
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from pathlib import Path

from elasticsearch import Elasticsearch, AsyncElasticsearch
from elasticsearch.helpers import bulk, async_bulk
from elasticsearch.exceptions import (
    ConnectionError, RequestError, NotFoundError, ConflictError
)
from loguru import logger
from pydantic import BaseModel, Field
from dotenv import load_dotenv
import ssl
import os


@dataclass
class ESConfig:
    """Elasticsearch配置类"""
    hosts: List[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    api_key: Optional[str] = None
    cloud_id: Optional[str] = None
    ca_certs: Optional[str] = None
    verify_certs: bool = True
    ssl_show_warn: bool = True
    timeout: int = 30
    max_retries: int = 3
    retry_on_timeout: bool = True

    def __post_init__(self):
        if self.hosts is None:
            self.hosts = ['http://localhost:9200']
        else:
            # 确保hosts包含scheme
            formatted_hosts = []
            for host in self.hosts:
                if not host.startswith(('http://', 'https://')):
                    formatted_hosts.append(f'http://{host}')
                else:
                    formatted_hosts.append(host)
            self.hosts = formatted_hosts


class ESResponse(BaseModel):
    """ES响应统一格式"""
    success: bool = Field(default=True)
    data: Optional[Any] = Field(default=None)
    message: str = Field(default="")
    error: Optional[str] = Field(default=None)
    total: Optional[int] = Field(default=None)
    took: Optional[int] = Field(default=None)


def load_es_config_from_env() -> ESConfig:
    """从环境变量加载ES配置"""
    load_dotenv()

    return ESConfig(
        hosts=os.getenv("ES_HOSTS", "localhost:9200").split(","),
        username=os.getenv("ES_USERNAME"),
        password=os.getenv("ES_PASSWORD"),
        api_key=os.getenv("ES_API_KEY"),
        cloud_id=os.getenv("ES_CLOUD_ID"),
        ca_certs=os.getenv("ES_CA_CERTS"),
        verify_certs=os.getenv("ES_VERIFY_CERTS", "true").lower() == "true",
        timeout=int(os.getenv("ES_TIMEOUT", "30")),
        max_retries=int(os.getenv("ES_MAX_RETRIES", "3"))
    )


class ElasticsearchClient:
    """Elasticsearch客户端类"""

    def __init__(self, config: ESConfig):
        self.config = config
        self._client: Optional[Elasticsearch] = None
        self._async_client: Optional[AsyncElasticsearch] = None
        self._setup_logger()

    def _setup_logger(self):
        """设置日志记录器"""
        logger.add(
            "logs/elasticsearch_{time:YYYY-MM-DD}.log",
            rotation="1 day",
            retention="30 days",
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
            encoding="utf-8"
        )

    def _build_client_config(self) -> Dict[str, Any]:
        """构建客户端配置"""
        client_config = {
            'hosts': self.config.hosts,
            'request_timeout': self.config.timeout,
            'max_retries': self.config.max_retries,
            'retry_on_timeout': self.config.retry_on_timeout,
        }

        # 认证配置
        if self.config.username and self.config.password:
            client_config['basic_auth'] = (self.config.username, self.config.password)
        elif self.config.api_key:
            client_config['api_key'] = self.config.api_key
        elif self.config.cloud_id:
            client_config['cloud_id'] = self.config.cloud_id

        # SSL配置
        if self.config.ca_certs:
            client_config['ca_certs'] = self.config.ca_certs
        client_config['verify_certs'] = self.config.verify_certs
        client_config['ssl_show_warn'] = self.config.ssl_show_warn

        return client_config


    @property
    def client(self) -> Elasticsearch:
        """获取同步客户端"""
        if self._client is None:
            config = self._build_client_config()
            self._client = Elasticsearch(**config)
            logger.info(f"创建ES同步客户端连接: {self.config.hosts}")
        return self._client

    @property
    def async_client(self) -> AsyncElasticsearch:
        """获取异步客户端"""
        if self._async_client is None:
            config = self._build_client_config()
            self._async_client = AsyncElasticsearch(**config)
            logger.info(f"创建ES异步客户端连接: {self.config.hosts}")
        return self._async_client

    def close(self):
        """关闭连接"""
        if self._client:
            self._client.close()
            logger.info("ES同步客户端连接已关闭")

    async def aclose(self):
        """关闭异步连接"""
        if self._async_client:
            await self._async_client.close()
            logger.info("ES异步客户端连接已关闭")

    def ping(self) -> ESResponse:
        """检查连接状态"""
        try:
            logger.info(f"正在连接ES集群: {self.config.hosts}")
            result = self.client.ping()
            if result:
                logger.info("ES连接正常")
                return ESResponse(success=True, message="ES连接正常")
            else:
                logger.error("ES ping返回False，可能的原因：认证失败、网络不通、ES服务未启动")
                return ESResponse(success=False, error="ES ping返回False，请检查认证信息和网络连接")
        except ConnectionError as e:
            logger.error(f"ES连接错误: {str(e)}")
            return ESResponse(success=False, error=f"连接错误: {str(e)}")
        except Exception as e:
            logger.error(f"ES连接检查异常: {str(e)}")
            logger.error(f"异常类型: {type(e).__name__}")
            return ESResponse(success=False, error=f"连接异常: {type(e).__name__}: {str(e)}")

    async def aping(self) -> ESResponse:
        """异步检查连接状态"""
        try:
            result = await self.async_client.ping()
            if result:
                logger.info("ES异步连接正常")
                return ESResponse(success=True, message="ES异步连接正常")
            else:
                logger.error("ES异步连接失败")
                return ESResponse(success=False, error="ES异步连接失败")
        except Exception as e:
            logger.error(f"ES异步连接检查异常: {str(e)}")
            return ESResponse(success=False, error=f"ES异步连接检查异常: {str(e)}")

    def get_cluster_health(self) -> ESResponse:
        """获取集群健康状态"""
        try:
            health = self.client.cluster.health()
            logger.info(f"集群健康状态: {health['status']}")
            return ESResponse(
                success=True,
                data=health,
                message=f"集群状态: {health['status']}"
            )
        except Exception as e:
            logger.error(f"获取集群健康状态失败: {str(e)}")
            return ESResponse(success=False, error=f"获取集群健康状态失败: {str(e)}")

    def get_cluster_info(self) -> ESResponse:
        """获取集群信息"""
        try:
            info = self.client.info()
            logger.info(f"ES版本: {info['version']['number']}")
            return ESResponse(
                success=True,
                data=info,
                message=f"ES版本: {info['version']['number']}"
            )
        except Exception as e:
            logger.error(f"获取集群信息失败: {str(e)}")
            return ESResponse(success=False, error=f"获取集群信息失败: {str(e)}")

    def test_connection_detailed(self) -> ESResponse:
        """详细的连接测试"""
        try:
            logger.info("开始详细连接测试...")

            # 测试每个节点
            for i, host in enumerate(self.config.hosts):
                logger.info(f"测试节点 {i+1}: {host}")

            # 尝试ping
            logger.info("执行ping测试...")
            ping_result = self.client.ping()
            logger.info(f"Ping结果: {ping_result}")

            if not ping_result:
                # 尝试获取更多信息
                try:
                    logger.info("尝试获取集群信息...")
                    info = self.client.info()
                    logger.info(f"集群信息获取成功: {info.get('cluster_name', 'unknown')}")
                    return ESResponse(
                        success=True,
                        data=info,
                        message="连接成功（通过info接口）"
                    )
                except Exception as info_e:
                    logger.error(f"获取集群信息也失败: {str(info_e)}")
                    return ESResponse(
                        success=False,
                        error=f"Ping失败且无法获取集群信息: {str(info_e)}"
                    )
            else:
                return ESResponse(success=True, message="Ping测试成功")

        except Exception as e:
            logger.error(f"详细连接测试失败: {str(e)}")
            logger.error(f"异常类型: {type(e).__name__}")
            import traceback
            logger.error(f"完整错误信息: {traceback.format_exc()}")
            return ESResponse(
                success=False,
                error=f"连接测试失败: {type(e).__name__}: {str(e)}"
            )

    # ------------------------------ 索引操作 ------------------------------
    def create_index(
        self,
        index_name: str,
        mapping: Optional[Dict] = None,
        settings: Optional[Dict] = None
    ) -> ESResponse:
        """创建索引（支持自定义映射和设置）"""
        try:
            if self.client.indices.exists(index=index_name):
                logger.warning(f"索引 {index_name} 已存在")
                return ESResponse(success=True, message=f"索引 {index_name} 已存在")

            index_body = {}
            if settings:
                index_body["settings"] = settings
            if mapping:
                index_body["mappings"] = mapping

            response = self.client.indices.create(index=index_name, body=index_body)
            logger.info(f"索引 {index_name} 创建成功")
            return ESResponse(
                success=True,
                data=response,
                message=f"索引 {index_name} 创建成功"
            )
        except RequestError as e:
            logger.error(f"创建索引失败（请求错误）：{str(e)}")
            return ESResponse(success=False, error=f"创建索引失败（请求错误）：{str(e)}")
        except Exception as e:
            logger.error(f"创建索引失败：{str(e)}")
            return ESResponse(success=False, error=f"创建索引失败：{str(e)}")

    def delete_index(self, index_name: str) -> ESResponse:
        """删除索引（支持通配符）"""
        try:
            if not self.client.indices.exists(index=index_name):
                logger.warning(f"索引 {index_name} 不存在")
                return ESResponse(success=True, message=f"索引 {index_name} 不存在")

            response = self.client.indices.delete(index=index_name)
            logger.info(f"索引 {index_name} 删除成功")
            return ESResponse(
                success=True,
                data=response,
                message=f"索引 {index_name} 删除成功"
            )
        except NotFoundError:
            logger.warning(f"删除失败：索引 {index_name} 不存在")
            return ESResponse(success=True, message=f"索引 {index_name} 不存在")
        except Exception as e:
            logger.error(f"删除索引失败：{str(e)}")
            return ESResponse(success=False, error=f"删除索引失败：{str(e)}")

    def index_exists(self, index_name: str) -> ESResponse:
        """检查索引是否存在"""
        try:
            exists = self.client.indices.exists(index=index_name)
            return ESResponse(
                success=True,
                data={"exists": exists},
                message=f"索引 {index_name} {'存在' if exists else '不存在'}"
            )
        except Exception as e:
            logger.error(f"检查索引存在性失败：{str(e)}")
            return ESResponse(success=False, error=f"检查索引存在性失败：{str(e)}")

    # ------------------------------ 文档操作 ------------------------------
    def insert_document(
        self,
        index_name: str,
        doc: Dict,
        doc_id: Optional[str] = None,
        refresh: bool = False
    ) -> ESResponse:
        """插入单条文档"""
        try:
            kwargs = {"index": index_name, "document": doc, "refresh": refresh}
            if doc_id:
                kwargs["id"] = doc_id

            response = self.client.index(**kwargs)
            doc_id = response["_id"]
            logger.debug(f"文档 {doc_id} 插入索引 {index_name} 成功")
            return ESResponse(
                success=True,
                data={"doc_id": doc_id, "response": response},
                message=f"文档 {doc_id} 插入成功"
            )
        except ConflictError:
            logger.error(f"插入失败：文档 ID {doc_id} 已存在")
            return ESResponse(success=False, error=f"文档 ID {doc_id} 已存在")
        except Exception as e:
            logger.error(f"插入文档失败：{str(e)}")
            return ESResponse(success=False, error=f"插入文档失败：{str(e)}")

    def get_document(
        self,
        index_name: str,
        doc_id: str,
        source: Optional[List[str]] = None
    ) -> ESResponse:
        """获取单条文档"""
        try:
            kwargs = {"index": index_name, "id": doc_id}
            if source:
                kwargs["_source"] = source

            response = self.client.get(**kwargs)
            logger.debug(f"获取文档 {doc_id} 成功")
            return ESResponse(
                success=True,
                data=response,
                message=f"获取文档 {doc_id} 成功"
            )
        except NotFoundError:
            logger.warning(f"文档 {doc_id} 不存在")
            return ESResponse(success=False, error=f"文档 {doc_id} 不存在")
        except Exception as e:
            logger.error(f"获取文档失败：{str(e)}")
            return ESResponse(success=False, error=f"获取文档失败：{str(e)}")

    def update_document(
        self,
        index_name: str,
        doc_id: str,
        doc: Dict,
        refresh: bool = False
    ) -> ESResponse:
        """更新文档"""
        try:
            response = self.client.update(
                index=index_name,
                id=doc_id,
                doc=doc,
                refresh=refresh
            )
            logger.debug(f"文档 {doc_id} 更新成功")
            return ESResponse(
                success=True,
                data=response,
                message=f"文档 {doc_id} 更新成功"
            )
        except NotFoundError:
            logger.error(f"更新失败：文档 {doc_id} 不存在")
            return ESResponse(success=False, error=f"文档 {doc_id} 不存在")
        except Exception as e:
            logger.error(f"更新文档失败：{str(e)}")
            return ESResponse(success=False, error=f"更新文档失败：{str(e)}")

    def delete_document(
        self,
        index_name: str,
        doc_id: str,
        refresh: bool = False
    ) -> ESResponse:
        """删除文档"""
        try:
            response = self.client.delete(
                index=index_name,
                id=doc_id,
                refresh=refresh
            )
            logger.debug(f"文档 {doc_id} 删除成功")
            return ESResponse(
                success=True,
                data=response,
                message=f"文档 {doc_id} 删除成功"
            )
        except NotFoundError:
            logger.warning(f"删除失败：文档 {doc_id} 不存在")
            return ESResponse(success=False, error=f"文档 {doc_id} 不存在")
        except Exception as e:
            logger.error(f"删除文档失败：{str(e)}")
            return ESResponse(success=False, error=f"删除文档失败：{str(e)}")

    def bulk_insert(
        self,
        index_name: str,
        docs: List[Dict],
        doc_ids: Optional[List[str]] = None,
        refresh: bool = False
    ) -> ESResponse:
        """批量插入文档"""
        if len(docs) == 0:
            logger.warning("批量插入：文档列表为空")
            return ESResponse(success=True, message="文档列表为空")

        if doc_ids and len(doc_ids) != len(docs):
            logger.error("批量插入失败：doc_ids 与 docs 长度不一致")
            return ESResponse(success=False, error="doc_ids 与 docs 长度不一致")

        operations = []
        for i, doc in enumerate(docs):
            op = {"_index": index_name, "_source": doc}
            if doc_ids:
                op["_id"] = doc_ids[i]
            operations.append(op)

        return self.bulk_operation(operations, refresh=refresh)

    def bulk_operation(
        self,
        operations: List[Dict],
        refresh: bool = False
    ) -> ESResponse:
        """批量执行操作（插入/更新/删除）"""
        if not operations:
            logger.warning("批量操作：操作列表为空")
            return ESResponse(success=True, message="操作列表为空")

        try:
            success, failed = bulk(
                self.client,
                operations,
                refresh=refresh,
                raise_on_error=False
            )

            if failed:
                logger.error(f"批量操作部分失败：成功 {success} 条，失败 {len(failed)} 条")
                for err in failed:
                    logger.error(f"失败详情：{json.dumps(err, ensure_ascii=False)}")
                return ESResponse(
                    success=False,
                    data={"success": success, "failed": failed},
                    error=f"批量操作部分失败：成功 {success} 条，失败 {len(failed)} 条"
                )
            else:
                logger.info(f"批量操作成功：共处理 {success} 条")
                return ESResponse(
                    success=True,
                    data={"success": success},
                    message=f"批量操作成功：共处理 {success} 条"
                )
        except Exception as e:
            logger.error(f"批量操作失败：{str(e)}")
            return ESResponse(success=False, error=f"批量操作失败：{str(e)}")

    async def async_bulk_operation(
        self,
        operations: List[Dict],
        refresh: bool = False
    ) -> ESResponse:
        """异步批量执行操作"""
        if not operations:
            logger.warning("异步批量操作：操作列表为空")
            return ESResponse(success=True, message="操作列表为空")

        try:
            success, failed = await async_bulk(
                self.async_client,
                operations,
                refresh=refresh,
                raise_on_error=False
            )

            if failed:
                logger.error(f"异步批量操作部分失败：成功 {success} 条，失败 {len(failed)} 条")
                return ESResponse(
                    success=False,
                    data={"success": success, "failed": failed},
                    error=f"异步批量操作部分失败：成功 {success} 条，失败 {len(failed)} 条"
                )
            else:
                logger.info(f"异步批量操作成功：共处理 {success} 条")
                return ESResponse(
                    success=True,
                    data={"success": success},
                    message=f"异步批量操作成功：共处理 {success} 条"
                )
        except Exception as e:
            logger.error(f"异步批量操作失败：{str(e)}")
            return ESResponse(success=False, error=f"异步批量操作失败：{str(e)}")

    # ------------------------------ 查询操作 ------------------------------
    def search(
        self,
        index_name: str,
        query: Dict,
        size: int = 10,
        from_: int = 0,
        sort: Optional[List[Dict]] = None,
        source: Optional[List[str]] = None,
        highlight: Optional[Dict] = None
    ) -> ESResponse:
        """执行查询（支持分页、排序、字段过滤、高亮）"""
        try:
            body = {"query": query}
            if sort:
                body["sort"] = sort
            if source is not None:
                body["_source"] = source
            if highlight:
                body["highlight"] = highlight

            response = self.client.search(
                index=index_name,
                body=body,
                size=size,
                from_=from_
            )

            total = response['hits']['total']['value']
            took = response['took']
            logger.debug(f"查询 {index_name} 成功，命中 {total} 条，耗时 {took}ms")

            return ESResponse(
                success=True,
                data=response,
                message=f"查询成功，命中 {total} 条",
                total=total,
                took=took
            )
        except NotFoundError:
            logger.warning(f"查询失败：索引 {index_name} 不存在")
            return ESResponse(success=False, error=f"索引 {index_name} 不存在")
        except Exception as e:
            logger.error(f"查询失败：{str(e)}")
            return ESResponse(success=False, error=f"查询失败：{str(e)}")

    def count(self, index_name: str, query: Dict) -> ESResponse:
        """统计文档数量"""
        try:
            response = self.client.count(index=index_name, body={"query": query})
            count = response["count"]
            return ESResponse(
                success=True,
                data={"count": count},
                message=f"统计成功，共 {count} 条",
                total=count
            )
        except NotFoundError:
            logger.warning(f"计数失败：索引 {index_name} 不存在")
            return ESResponse(success=False, error=f"索引 {index_name} 不存在")
        except Exception as e:
            logger.error(f"计数失败：{str(e)}")
            return ESResponse(success=False, error=f"计数失败：{str(e)}")

    def multi_search(
        self,
        searches: List[Dict]
    ) -> ESResponse:
        """多重搜索"""
        try:
            body = []
            for search in searches:
                body.append({"index": search.get("index")})
                body.append(search.get("body", {}))

            response = self.client.msearch(body=body)
            return ESResponse(
                success=True,
                data=response,
                message=f"多重搜索成功，共 {len(searches)} 个查询"
            )
        except Exception as e:
            logger.error(f"多重搜索失败：{str(e)}")
            return ESResponse(success=False, error=f"多重搜索失败：{str(e)}")

    def scroll_search(
        self,
        index_name: str,
        query: Dict,
        scroll: str = "5m",
        size: int = 1000
    ) -> ESResponse:
        """滚动搜索（用于大量数据）"""
        try:
            response = self.client.search(
                index=index_name,
                body={"query": query},
                scroll=scroll,
                size=size
            )

            scroll_id = response.get('_scroll_id')
            hits = response['hits']['hits']
            total = response['hits']['total']['value']

            return ESResponse(
                success=True,
                data={
                    "scroll_id": scroll_id,
                    "hits": hits,
                    "total": total
                },
                message=f"滚动搜索成功，本次返回 {len(hits)} 条",
                total=total
            )
        except Exception as e:
            logger.error(f"滚动搜索失败：{str(e)}")
            return ESResponse(success=False, error=f"滚动搜索失败：{str(e)}")

    def scroll_next(self, scroll_id: str, scroll: str = "5m") -> ESResponse:
        """获取下一批滚动数据"""
        try:
            response = self.client.scroll(
                scroll_id=scroll_id,
                scroll=scroll
            )

            hits = response['hits']['hits']
            return ESResponse(
                success=True,
                data={
                    "scroll_id": response.get('_scroll_id'),
                    "hits": hits
                },
                message=f"获取下一批数据成功，返回 {len(hits)} 条"
            )
        except Exception as e:
            logger.error(f"获取下一批滚动数据失败：{str(e)}")
            return ESResponse(success=False, error=f"获取下一批滚动数据失败：{str(e)}")

    def clear_scroll(self, scroll_id: str) -> ESResponse:
        """清除滚动上下文"""
        try:
            response = self.client.clear_scroll(scroll_id=scroll_id)
            return ESResponse(
                success=True,
                data=response,
                message="清除滚动上下文成功"
            )
        except Exception as e:
            logger.error(f"清除滚动上下文失败：{str(e)}")
            return ESResponse(success=False, error=f"清除滚动上下文失败：{str(e)}")

    # ------------------------------ 聚合查询 ------------------------------
    def aggregate(
        self,
        index_name: str,
        aggs: Dict,
        query: Optional[Dict] = None,
        size: int = 0
    ) -> ESResponse:
        """执行聚合查询"""
        try:
            body = {"aggs": aggs, "size": size}
            if query:
                body["query"] = query

            response = self.client.search(
                index=index_name,
                body=body
            )

            return ESResponse(
                success=True,
                data=response,
                message="聚合查询成功",
                took=response.get('took')
            )
        except Exception as e:
            logger.error(f"聚合查询失败：{str(e)}")
            return ESResponse(success=False, error=f"聚合查询失败：{str(e)}")

    # ------------------------------ 模板操作 ------------------------------
    def put_index_template(
        self,
        name: str,
        template: Dict
    ) -> ESResponse:
        """创建或更新索引模板"""
        try:
            response = self.client.indices.put_index_template(
                name=name,
                body=template
            )
            return ESResponse(
                success=True,
                data=response,
                message=f"索引模板 {name} 创建/更新成功"
            )
        except Exception as e:
            logger.error(f"创建/更新索引模板失败：{str(e)}")
            return ESResponse(success=False, error=f"创建/更新索引模板失败：{str(e)}")

    def get_index_template(self, name: str) -> ESResponse:
        """获取索引模板"""
        try:
            response = self.client.indices.get_index_template(name=name)
            return ESResponse(
                success=True,
                data=response,
                message=f"获取索引模板 {name} 成功"
            )
        except NotFoundError:
            return ESResponse(success=False, error=f"索引模板 {name} 不存在")
        except Exception as e:
            logger.error(f"获取索引模板失败：{str(e)}")
            return ESResponse(success=False, error=f"获取索引模板失败：{str(e)}")

    def delete_index_template(self, name: str) -> ESResponse:
        """删除索引模板"""
        try:
            response = self.client.indices.delete_index_template(name=name)
            return ESResponse(
                success=True,
                data=response,
                message=f"删除索引模板 {name} 成功"
            )
        except NotFoundError:
            return ESResponse(success=False, error=f"索引模板 {name} 不存在")
        except Exception as e:
            logger.error(f"删除索引模板失败：{str(e)}")
            return ESResponse(success=False, error=f"删除索引模板失败：{str(e)}")

    # ------------------------------ 节点信息 ------------------------------
    def get_nodes_info(self) -> ESResponse:
        """获取节点信息"""
        try:
            response = self.client.nodes.info()
            return ESResponse(
                success=True,
                data=response,
                message="获取节点信息成功"
            )
        except Exception as e:
            logger.error(f"获取节点信息失败：{str(e)}")
            return ESResponse(success=False, error=f"获取节点信息失败：{str(e)}")

    def get_nodes_stats(self) -> ESResponse:
        """获取节点统计信息"""
        try:
            response = self.client.nodes.stats()
            return ESResponse(
                success=True,
                data=response,
                message="获取节点统计信息成功"
            )
        except Exception as e:
            logger.error(f"获取节点统计信息失败：{str(e)}")
            return ESResponse(success=False, error=f"获取节点统计信息失败：{str(e)}")


# ------------------------------ 使用示例 ------------------------------
if __name__ == "__main__":
    # 创建配置
    config = ESConfig(
        hosts=["localhost:9200"],
        username="elastic",
        password="password",
        # proxy_host="proxy.example.com",
        # proxy_port=8080,
        timeout=30
    )

    # 创建客户端
    es_client = ElasticsearchClient(config)

    # 检查连接
    ping_result = es_client.ping()
    print(f"连接状态: {ping_result.message}")

    if ping_result.success:
        # 获取集群信息
        info_result = es_client.get_cluster_info()
        print(f"集群信息: {info_result.message}")

        # 创建索引
        index_result = es_client.create_index("test_index")
        print(f"创建索引: {index_result.message}")

        # 插入文档
        doc = {"title": "测试文档", "content": "这是一个测试文档"}
        insert_result = es_client.insert_document("test_index", doc)
        print(f"插入文档: {insert_result.message}")

        # 搜索文档
        query = {"match_all": {}}
        search_result = es_client.search("test_index", query)
        print(f"搜索结果: {search_result.message}")

    # 关闭连接
    es_client.close()