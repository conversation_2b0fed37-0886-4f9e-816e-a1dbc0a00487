"""
关键词索引管理器
基于filtered_keywords-Copy3.json数据设计的索引结构和检索功能
"""

import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from loguru import logger
from pydantic import BaseModel, Field

from es_client import ElasticsearchClient, ESConfig, ESResponse


class KeywordDocument(BaseModel):
    """关键词文档模型"""
    chunk_id: str = Field(description="文档块ID")
    keywords: List[str] = Field(description="关键词列表")
    keyword_count: int = Field(description="关键词数量")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class KeywordIndexManager:
    """关键词索引管理器"""
    
    def __init__(self, es_client: ElasticsearchClient, index_name: str = "keyword_documents"):
        self.es_client = es_client
        self.index_name = index_name
        self.setup_logger()
        
    def setup_logger(self):
        """设置日志"""
        logger.add(
            "logs/keyword_index_{time:YYYY-MM-DD}.log",
            rotation="1 day",
            retention="30 days",
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
            encoding="utf-8"
        )
        
    def get_index_mapping(self) -> Dict[str, Any]:
        """获取索引映射配置"""
        return {
            "properties": {
                "chunk_id": {
                    "type": "keyword",
                    "fields": {
                        "text": {
                            "type": "text",
                            "analyzer": "standard"
                        }
                    }
                },
                "keywords": {
                    "type": "text",
                    "analyzer": "ik_max_word",
                    "search_analyzer": "ik_smart",
                    "fields": {
                        "keyword": {
                            "type": "keyword"
                        },
                        "suggest": {
                            "type": "completion",
                            "analyzer": "simple"
                        }
                    }
                },
                "keyword_count": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
                },
                "updated_at": {
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
                }
            }
        }
        
    def get_index_settings(self) -> Dict[str, Any]:
        """获取索引设置"""
        return {
            "number_of_shards": 1,
            "number_of_replicas": 1,
            "analysis": {
                "analyzer": {
                    "ik_max_word": {
                        "type": "custom",
                        "tokenizer": "ik_max_word",
                        "filter": ["lowercase", "stop"]
                    },
                    "ik_smart": {
                        "type": "custom", 
                        "tokenizer": "ik_smart",
                        "filter": ["lowercase", "stop"]
                    },
                    "keyword_analyzer": {
                        "type": "custom",
                        "tokenizer": "keyword",
                        "filter": ["lowercase", "trim"]
                    }
                },
                "tokenizer": {
                    "ik_max_word": {
                        "type": "ik_max_word"
                    },
                    "ik_smart": {
                        "type": "ik_smart"
                    }
                }
            },
            "max_result_window": 50000
        }
        
    def create_index(self) -> ESResponse:
        """创建索引"""
        logger.info(f"开始创建索引: {self.index_name}")
        
        # 检查索引是否已存在
        exists_result = self.es_client.index_exists(self.index_name)
        if exists_result.success and exists_result.data.get("exists"):
            logger.warning(f"索引 {self.index_name} 已存在")
            return ESResponse(success=True, message=f"索引 {self.index_name} 已存在")
        
        # 创建索引
        mapping = self.get_index_mapping()
        settings = self.get_index_settings()
        
        result = self.es_client.create_index(
            index_name=self.index_name,
            mapping=mapping,
            settings=settings
        )
        
        if result.success:
            logger.info(f"索引 {self.index_name} 创建成功")
        else:
            logger.error(f"索引 {self.index_name} 创建失败: {result.error}")
            
        return result
        
    def delete_index(self) -> ESResponse:
        """删除索引"""
        logger.info(f"开始删除索引: {self.index_name}")
        result = self.es_client.delete_index(self.index_name)
        
        if result.success:
            logger.info(f"索引 {self.index_name} 删除成功")
        else:
            logger.error(f"索引 {self.index_name} 删除失败: {result.error}")
            
        return result
        
    def load_data_from_json(self, json_file_path: str) -> ESResponse:
        """从JSON文件加载数据"""
        logger.info(f"开始从文件加载数据: {json_file_path}")
        
        try:
            # 读取JSON文件
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 转换数据格式
            documents = []
            for chunk_id, keywords in data.items():
                doc = KeywordDocument(
                    chunk_id=chunk_id,
                    keywords=keywords,
                    keyword_count=len(keywords)
                )
                documents.append(doc.dict())
            
            logger.info(f"准备导入 {len(documents)} 条文档")
            
            # 批量插入
            result = self.es_client.bulk_insert(
                index_name=self.index_name,
                docs=documents,
                refresh=True
            )
            
            if result.success:
                logger.info(f"数据导入成功，共导入 {len(documents)} 条文档")
            else:
                logger.error(f"数据导入失败: {result.error}")
                
            return result
            
        except FileNotFoundError:
            error_msg = f"文件不存在: {json_file_path}"
            logger.error(error_msg)
            return ESResponse(success=False, error=error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"JSON文件格式错误: {str(e)}"
            logger.error(error_msg)
            return ESResponse(success=False, error=error_msg)
        except Exception as e:
            error_msg = f"加载数据失败: {str(e)}"
            logger.error(error_msg)
            return ESResponse(success=False, error=error_msg)
            
    def search_by_keyword(
        self,
        keyword: str,
        size: int = 10,
        from_: int = 0,
        highlight: bool = True
    ) -> ESResponse:
        """根据关键词搜索文档"""
        logger.info(f"搜索关键词: {keyword}")
        
        # 构建查询
        query = {
            "bool": {
                "should": [
                    {
                        "match": {
                            "keywords": {
                                "query": keyword,
                                "boost": 2.0
                            }
                        }
                    },
                    {
                        "term": {
                            "keywords.keyword": {
                                "value": keyword,
                                "boost": 3.0
                            }
                        }
                    },
                    {
                        "wildcard": {
                            "keywords.keyword": {
                                "value": f"*{keyword}*",
                                "boost": 1.0
                            }
                        }
                    }
                ],
                "minimum_should_match": 1
            }
        }
        
        # 高亮配置
        highlight_config = None
        if highlight:
            highlight_config = {
                "fields": {
                    "keywords": {
                        "pre_tags": ["<mark>"],
                        "post_tags": ["</mark>"],
                        "fragment_size": 100,
                        "number_of_fragments": 3
                    }
                }
            }
        
        # 排序配置
        sort = [
            {"_score": {"order": "desc"}},
            {"keyword_count": {"order": "desc"}},
            {"created_at": {"order": "desc"}}
        ]
        
        result = self.es_client.search(
            index_name=self.index_name,
            query=query,
            size=size,
            from_=from_,
            sort=sort,
            highlight=highlight_config
        )
        
        if result.success:
            logger.info(f"关键词搜索成功，找到 {result.total} 条结果")
        else:
            logger.error(f"关键词搜索失败: {result.error}")
            
        return result
        
    def search_by_chunk_id(self, chunk_id: str) -> ESResponse:
        """根据文档块ID搜索"""
        logger.info(f"搜索文档块ID: {chunk_id}")
        
        query = {
            "term": {
                "chunk_id": chunk_id
            }
        }
        
        result = self.es_client.search(
            index_name=self.index_name,
            query=query,
            size=1
        )
        
        if result.success:
            logger.info(f"文档块ID搜索成功")
        else:
            logger.error(f"文档块ID搜索失败: {result.error}")
            
        return result
        
    def get_keyword_suggestions(self, prefix: str, size: int = 10) -> ESResponse:
        """获取关键词建议"""
        logger.info(f"获取关键词建议: {prefix}")
        
        query = {
            "bool": {
                "should": [
                    {
                        "prefix": {
                            "keywords.keyword": prefix
                        }
                    },
                    {
                        "wildcard": {
                            "keywords.keyword": f"*{prefix}*"
                        }
                    }
                ]
            }
        }
        
        # 聚合获取唯一关键词
        aggs = {
            "unique_keywords": {
                "terms": {
                    "field": "keywords.keyword",
                    "include": f".*{prefix}.*",
                    "size": size
                }
            }
        }
        
        result = self.es_client.aggregate(
            index_name=self.index_name,
            aggs=aggs,
            query=query,
            size=0
        )
        
        if result.success:
            logger.info(f"关键词建议获取成功")
        else:
            logger.error(f"关键词建议获取失败: {result.error}")
            
        return result
