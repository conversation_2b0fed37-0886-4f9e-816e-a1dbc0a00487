{"chunk_0129_4972_3190.txt": ["序列", "上下文", "向量", "线程组", "块", "Warp", "线程块", "网格", "分页注意力"], "chunk_0103_5804_5332.txt": ["INT8", "量化", "权重", "激活", "NVIDIA GPU", "Turing", "Ampere", "<PERSON>", "<PERSON>"], "chunk_0073_7647_7811.txt": ["多模态输入", "SupportsMultiModal", "模型更新", "HuggingFace Transformers", "pixel_values", "forward()", "接口实现", "输入张量", "关键字参数"], "chunk_0122_8079_0147.txt": ["KV 缓存", "哈希表", "缓存策略", "引用计数", "LRU", "前缀树", "LoRA 适配器", "多模态模型", "感知哈希"], "chunk_0193_0534_5516.txt": ["量化", "AWQ", "Qwen2.5-3B-Instruct-AWQ", "边缘设备", "GPU", "语言模型", "内存使用", "计算效率", "部署"], "chunk_0263_7268_0724.txt": ["输入处理管道", "开发者文档", "输入处理", "指南", "vllm.ai"], "chunk_0095_4532_3797.txt": ["分块预填充", "GPU 利用率", "ITL", "TTFT", "max_num_batched_tokens", "解码请求", "预填充操作", "调度策略", "Llama-2-7b-hf"], "chunk_0125_7585_5332.txt": ["vllm.SamplingParams", "文本生成", "采样参数", "presence_penalty", "frequency_penalty", "repetition_penalty", "temperature", "top_p", "top_k", "束搜索"], "chunk_0051_3274_7601.txt": ["环境变量", "VLLM_PORT", "VLLM_HOST_IP", "Kubernetes", "CUDA_VISIBLE_DEVICES", "NCCL", "FlashAttention", "多模态模型", "OpenVINO"], "chunk_0158_6816_7601.txt": ["vllm", "vllm.engine", "vllm.inputs.registry", "vllm.multimodal", "vllm.multimodal.image"], "chunk_0233_1686_2171.txt": ["SkyPilot", "服务副本", "Llama-3 8B 模型", "自动缩放", "准备就绪探针", "负载均衡", "Gradio", "端点", "加速器"], "chunk_0179_1478_3139.txt": ["AWQ 量化", "Qwen2.5-3B-Instruct", "少样本学习", "SamplingParams", "AutoTokenizer", "AutoModel", "CUDA_VISIBLE_DEVICES", "cosine_similarity", "cdist"], "chunk_0011_2376_5332.txt": ["OpenVINO", "LLM 模型", "x86-64 CPU", "AVX2", "前缀缓存", "分块预填充", "模型服务", "dockerfile", "性能提示"], "chunk_0056_4165_5332.txt": ["兼容性矩阵", "互斥的特性", "硬件支持", "跟踪问题", "特性组合"], "chunk_0170_4039_0008.txt": ["Qwen2.5-3B-Instruct-AWQ", "量化模型", "AWQ", "GPU内存利用率", "tokenizer", "模型加载", "CUDA_VISIBLE_DEVICES", "max_model_len", "enforce_eager"], "chunk_0043_6133_6038.txt": ["LoRA", "tokenizer", "quantization", "speculative decoding", "RoPE", "CUDA", "distributed executor", "block size", "prompt adapter", "model loading"], "chunk_0136_4972_1153.txt": ["vllm分页注意力", "局部寄存器内存", "全局内存", "输出", "HEAD_SIZE", "num_heads", "max_num_partitions", "out_ptr", "累积结果", "序列"], "chunk_0115_4030_7601.txt": ["FP8", "量化", "KV 缓存", "GPU 内存", "int8", "int4", "尾数位", "数据格式", "LLM", "SamplingParams"], "chunk_0123_4708_5332.txt": ["性能基准测试", "Nightly 基准测试", "工作负载", "tgi", "trt-llm", "lmdeploy", "性能仪表板", "拉取请求", "基准套件"], "chunk_0262_7268_5332.txt": ["INPUT_REGISTRY", "MULTIMODAL_REGISTRY", "输入处理管道", "多模态模型", "多模态", "预处理", "输入提示", "模型", "input_processing_pipeline"], "chunk_0018_7136_9030.txt": ["依赖环境", "操作系统", "Linux", "编译器", "gcc/g++", "指令集架构", "AVX512"], "chunk_0120_0507_7101.txt": ["APC", "预填充阶段", "处理查询", "生成新 token", "性能提升", "答案长度", "前缀共享", "计算重复使用"], "chunk_0222_4990_7601.txt": ["音频 API", "OpenAI", "base64 编码", "librosa", "chat.completions", "AudioAsset", "模型服务", "winning_call", "fixie-ai/ultravox-v0_3"], "chunk_0195_2393_0411.txt": ["离线推理", "LoRA 推理", "MultiLoRA 推理", "Gradio Web 服务器", "OpenAI API 客户端", "GGUF 推理", "量化", "LLM 引擎", "视觉语言", "Tensorize vLLM 模型"], "chunk_0196_7199_7601.txt": ["vllm", "API 客户端", "api_server", "vllm serve", "OpenAI 客户端 API", "HTTP 请求", "流式响应", "生成模型", "性能基准测试", "Python 示例"], "chunk_0006_4287_1698.txt": ["源代码构建", "Linux", "CUDA 工具包", "pip install -e .", "ccache", "MAX_JOBS", "NVIDIA PyTorch Docker 镜像", "nvcc", "共享内存"], "chunk_0121_8079_5332.txt": ["PagedAttention", "KV 缓存", "KV 块", "自动前缀缓存", "注意力键", "注意力值", "内存碎片", "按需分配", "哈希值", "物理块"], "chunk_0085_6640_3965.txt": ["LoRA 模型", "模型卡", "父模型", "Llama-2-7b-hf", "sql-lora", "lora 适配器", "模型谱系", "root 字段", "huggingface", "适配器工件"], "chunk_0045_6133_5597.txt": ["工具调用", "chat completion API", "Hermes 模型", "Mistral 模型", "tool_call_parser", "chat-template", "自动函数调用", "tokenizer_config.json", "并行工具调用"], "chunk_0132_4972_9690.txt": ["分页注意力", "QK", "点乘运算", "线程组", "THREAD_GROUP_SIZE", "归约操作", "HEAD_SIZE", "k_vecs", "q_vecs", "伪代码"], "chunk_0255_7311_5332.txt": ["LLMEngine", "KV缓存", "分布式执行", "请求调度", "文本生成", "SpeculativeConfig", "LoRAConfig", "SamplingParams", "SequenceGroup"], "chunk_0252_7401_5272.txt": ["TextPrompt", "vllm.inputs", "multi_modal_data", "mm_processor_kwargs", "TypedDict", "输入文本", "多模态数据", "多模态处理器", "模型支持", "标记化"], "chunk_0028_0142_9030.txt": ["TPU", "Google Cloud TPU VM", "v5e", "v5p", "v4", "Python 3.10", "Dockerfile.tpu", "Docker 镜像", "源代码构建", "依赖环境"], "chunk_0201_5265_7601.txt": ["Gradio", "Web 服务器", "文本补全", "HTTP 请求", "模型接口", "参数解析", "流式响应", "Python 脚本", "API 调用"], "chunk_0004_4287_9030.txt": ["依赖环境", "Linux", "Python", "GPU", "计算能力", "V100", "T4", "RTX20xx", "A100", "H100"], "chunk_0254_9816_7601.txt": ["LLM Class", "LLM Inputs", "离线推理", "vllm", "开发者文档"], "chunk_0087_2272_7958.txt": ["离线推理", "视觉语言模型", "VLM", "多图像输入", "图像嵌入", "LLM", "Phi-3.5-vision", "Qwen2-VL", "PIL.Image", "多模态数据"], "chunk_0007_9752_5332.txt": ["ROCm", "AMD GPU", "安装", "GPU"], "chunk_0026_1485_1698.txt": ["Neuron SDK", "Trn1/Inf2 实例", "Ubuntu 22.04 LTS", "Neuron 驱动", "Neuron Runtime", "transformers-neuronx", "Neuron 编译器", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AWS <PERSON>"], "chunk_0091_4092_4116.txt": ["推测解码", "n-grams", "提示符", "SamplingParams", "LLM", "生成文本", "ngram_prompt_lookup_max", "speculative_model", "num_speculative_tokens"], "chunk_0049_9931_5217.txt": ["多节点推理", "分布式推理", "GPU", "Ray 集群", "docker 镜像", "张量并行", "管道并行", "Infiniband", "NCCL", "Huggingface"], "chunk_0228_2758_7601.txt": ["保存分片状态", "张量并行模型", "checkpoint", "LLM", "sharded_state", "deepspeedfp", "模型状态字典", "safetensors", "模型加载"], "chunk_0031_2546_5332.txt": ["Intel GPU", "XPU", "Dockerfile", "模型推理", "源代码构建", "依赖环境"], "chunk_0202_5469_7601.txt": ["LLMEngine", "SamplingParams", "RequestOutput", "EngineArgs", "prompt processing", "test prompts", "beam search", "temperature", "top_p"], "chunk_0246_0743_0257.txt": ["encode", "RequestOutput", "PoolingParams", "prompt_token_ids", "LoRARequest", "EmbeddingRequestOutput", "离线推理", "LLM", "对话返回", "批处理"], "chunk_0078_0159_5332.txt": ["引擎参数", "模型", "tokenizer", "量化", "并行加载", "GPU内存利用率", "块大小", "LORA", "speculative decoding"], "chunk_0039_6133_5332.txt": ["OpenAI 兼容服务器", "Completions API", "Chat API", "HTTP 服务器", "NousResearch/Meta-Llama-3-8B-Instruct", "<PERSON>er", "api-key", "OpenAI Python 客户端库", "模型部署"], "chunk_0069_9128_4283.txt": ["张量并行", "量化支持", "VocabParallel<PERSON>", "ParallelLMHead", "ReplicatedLinear", "RowParallelLinear", "ColumnParallelLinear", "MergedColumnParallelLinear", "QKVParallelLinear", "权重加载逻辑"], "chunk_0192_0534_6855.txt": ["多 GPU 推理", "分布式张量并行推理", "高吞吐量系统", "大型模型", "tensor_parallel_size", "GPU", "模型扩展", "计算量", "Qwen2.5-1.5B-Instruct"], "chunk_0240_3879_7601.txt": ["Langchain", "GPU", "推理", "HF 型号", "langchain_community", "VLLM 类", "max_new_tokens", "top_k", "top_p"], "chunk_0167_4039_5332.txt": ["Qwen2.5", "推理", "模型加载", "数据准备", "优化", "结果提取", "评估", "大语言模型", "参数"], "chunk_0259_7311_9439.txt": ["LLMEngine", "SamplingParams", "请求参数", "事件循环", "请求处理", "example/ 文件夹", "temperature", "引擎初始化", "请求输出", "unfinished requests"], "chunk_0253_7401_7924.txt": ["TokensPrompt", "prompt_token_ids", "多模态数据", "mm_processor_kwargs", "token ID", "多模态处理器", "输入映射器", "vllm.inputs", "离线推理", "模型"], "chunk_0164_7177_6319.txt": ["<PERSON>wen-1_8B-<PERSON><PERSON>", "HuggingFace", "离线推理", "模型准备", "SamplingParams", "Python API", "Git LFS", "通义千问", "max_model_len"], "chunk_0059_1405_5332.txt": ["OpenAI API", "多个模型服务", "logprobs", "数值不稳定性", "批处理", "嵌入模型", "e5-mistral-7b-instruct", "Llama-3-8b", "Mistral-7B-Instruct-v0.3"], "chunk_0111_1496_8233.txt": ["量化过程", "FP8_DYNAMIC", "Linear层", "RTN量化", "模型加载", "模型保存", "lm_eval", "gsm8k", "exact_match"], "chunk_0210_2425_7601.txt": ["Ray <PERSON>", "分布式推理", "离线批量推断", "PlacementGroupSchedulingStrategy", "S3", "Pa<PERSON><PERSON>", "SamplingParams", "LLM", "tensor parallelism"], "chunk_0113_1496_1093.txt": ["FP8", "W8A8", "AutoFP8", "静态每张量离线量化", "llmcompressor", "BaseQuantizeConfig", "AutoFP8ForCausalLM", "量化", "模型压缩"], "chunk_0165_7177_9493.txt": ["vLLM 服务器", "OpenAI API", "HuggingFace 模型", "参数设置", "启动命令行", "模型权重", "张量并行", "上下文长度", "分布式服务", "API 协议"], "chunk_0229_7444_7601.txt": ["Tensorizer", "序列化", "反序列化", "模型权重", "S3", "GPU加载", "加密", "分片模型", "OpenAI推理服务器"], "chunk_0220_9880_7601.txt": ["前缀缓存", "离线推理", "SamplingParams", "LLM", "KV缓存", "生成文本", "模型加速", "OPT-125m", "面板面试问题"], "chunk_0090_4092_4302.txt": ["推测解码", "草稿模型", "离线模式", "在线模式", "API 服务器", "token", "SamplingParams", "LLM", "num_speculative_tokens"], "chunk_0015_2376_1861.txt": ["vLLM OpenVINO 后端", "KV 缓存", "VLLM_OPENVINO_KVCACHE_SPACE", "VLLM_OPENVINO_CPU_KV_CACHE_PRECISION", "VLLM_OPENVINO_ENABLE_QUANTIZED_WEIGHTS", "U8 权重压缩", "分块预填充", "TPOT / TTFT 延迟", "max-num-batched-tokens", "OpenVINO 配置"], "chunk_0248_0743_0257.txt": ["generate", "prompts", "prompt_token_ids", "SamplingParams", "RequestOutput", "LoRARequest", "离线推理", "LLM", "EmbeddingRequestOutput", "批处理"], "chunk_0037_1188_7976.txt": ["OpenAI API", "Chat API", "Completions API", "OPT-125M", "API 服务器", "聊天模板", "API 密钥", "模型列表", "动态对话"], "chunk_0012_2376_9030.txt": ["OpenVINO", "依赖环境", "操作系统", "Linux", "指令集架构", "AVX2"], "chunk_0218_9575_7601.txt": ["离线推理", "视觉语言模型", "HuggingFace", "LLaVA", "Phi-3-Vision", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Chameleon", "MiniCPM-V", "InternVL"], "chunk_0025_1485_9030.txt": ["NeuronCore_v2", "Trn1/Inf2 实例", "AWS Neuron SDK", "Transformers-neuronx", "PyTorch", "Linux", "依赖环境"], "chunk_0038_4587_2643.txt": ["挂起", "崩溃", "张量并行", "CUDA 内核", "NCCL", "模型加载", "分布式文件系统", "环境变量", "cudagraph"], "chunk_0014_2376_0245.txt": ["OpenVINO", "源代码安装", "Python", "Ubuntu 22.04", "pip", "requirements-build.txt", "PyTorch", "安装依赖", "OpenVINO 后端"], "chunk_0133_4972_9635.txt": ["softmax", "qk_max", "logits", "exp_sum", "线程块", "归约操作", "分页注意力", "vllm", "warp", "共享内存"], "chunk_0003_4287_5332.txt": ["Python 库", "C++", "CUDA", "二进制文件", "预编译"], "chunk_0117_0507_4242.txt": ["自动前缀缓存", "APC", "KV 缓存", "查询", "缓存重用"], "chunk_0046_4818_7601.txt": ["Docker 部署", "Docker 镜像", "NVIDIA GPU", "PyTorch", "共享内存", "张量并行推理", "Hugging Face", "libnccl.so.2.18.1", "vLLM-OpenAI"], "chunk_0258_7311_0758.txt": ["token 块", "序列组", "调度策略", "分布式执行器", "beam_search", "模型输出", "采样参数", "生成结果"], "chunk_0076_7647_0505.txt": ["虚拟数据", "多模态输入", "INPUT_REGISTRY.register_dummy_data", "多模态模型", "图像输入", "MULTIMODAL_REGISTRY", "LLaVA-1.5", "LLaVA-NeXT", "输入处理管道"], "chunk_0251_7401_6545.txt": ["vllm.inputs.PromptType", "Union", "TextPrompt", "TokensPrompt", "ExplicitEncoderDecoderPrompt", "collections.abc", "Typing.List", "Typing.Dict", "离线推理", "PromptType"], "chunk_0227_7840_7601.txt": ["VLMs", "OpenAI API", "视觉 API", "Llava", "Phi-3.5-vision-instruct", "base64 编码", "多图像推理", "单图像推理", "模型服务"], "chunk_0203_0435_7601.txt": ["LoRA", "量化", "QLoRA", "AWQ", "GPTQ", "HuggingFace", "LLMEngine", "推理", "适配器"], "chunk_0209_2633_7601.txt": ["Meta-Llama-3-8B-Instruct", "chat 方法", "离线对话推理", "SamplingParams", "LLM", "对话模板", "生成文本", "offline_inference_chat.py", "对话系统"], "chunk_0030_0142_1698.txt": ["TPU", "XLA", "PyTorch", "torch-xla", "JAX", "Pallas", "conda 环境", "XLA 图", "libopenblas.so.0"], "chunk_0266_3025_7601.txt": ["多模态插件", "MultiModalPlugin", "MULTIMODAL_REGISTRY", "register_plugin", "模态类型", "开发者文档", "自定义插件"], "chunk_0114_1496_3913.txt": ["FP8", "静态激活缩放因子", "离线量化", "AutoFP8", "权重和激活值", "校准数据", "BaseQuantizeConfig", "Meta-Llama-3-8B-Instruct", "量化配置"], "chunk_0054_1121_7954.txt": ["VLLM_NO_USAGE_STATS", "DO_NOT_TRACK", "使用统计数据收集", "退出使用统计数据收集", "~/.config/vllm/do_not_track"], "chunk_0194_0534_6706.txt": ["分布式 GPU", "<PERSON><PERSON><PERSON><PERSON>", "量化技术", "API 兼容性", "CUDA 优化内核", "连续批处理策略", "响应时间", "推理延迟", "生产系统"], "chunk_0058_4165_2800.txt": ["Volta", "Turing", "Ampere", "Ada", "<PERSON>", "CP", "APC", "LoRA", "CUDA graph", "enc-dec"], "chunk_0128_4972_3510.txt": ["内核函数", "参数列表", "输入指针", "q", "k_cache", "v_cache", "全局内存", "输出指针", "out", "分页注意力", "线程块", "HEAD_SIZE", "BLOCK_SIZE", "NUM_THREADS", "模板参数"], "chunk_0024_1485_5332.txt": ["vLLM 0.3.3", "Neuron SDK", "AWS Trainium", "Inferentia", "模型推理", "服务", "Transformers-neuronx", "连续批处理", "FP16", "BF16"], "chunk_0269_3267_8612.txt": ["MultiModalRegistry", "MultiModalPlugin", "输入映射器", "多模态数据", "图像类", "注册插件", "BatchedTensorInputs", "NestedTensors", "多模态输入处理", "ImagePlugin"], "chunk_0052_1121_5332.txt": ["匿名使用数据", "工程团队", "硬件", "模型配置", "工作负载", "敏感信息", "社区"], "chunk_0074_7647_4869.txt": ["注册输入映射器", "多模态输入", "MULTIMODAL_REGISTRY", "图像输入映射器", "输入处理管道", "SupportsMultiModal", "forward()", "关键字参数", "模态类型", "默认映射器"], "chunk_0172_4039_6966.txt": ["提示工程", "logits 处理器", "系统提示", "tokenizer", "模型输出", "标记", "Qwen2.5", "微调", "CV 分数"], "chunk_0013_2376_2306.txt": ["Dockerfile", "OpenVINO", "docker build", "docker run", "vllm-openvino-env", "安装", "快速开始", "环境构建"], "chunk_0200_9025_7601.txt": ["Gradio", "OpenAI", "聊天机器人", "API 服务器", "文本生成", "模型参数", "流式响应", "模型名称", "停止标记"], "chunk_0100_5968_8577.txt": ["量化", "BitsAndBytes", "checkpoint", "TinyLLama", "4bit", "模型加载", "bfloat16", "trust_remote_code", "load_format"], "chunk_0205_1664_7601.txt": ["Mistral-7B-Instruct-v0.3", "函数调用", "离线聊天工具", "API 模拟", "采样参数", "工具消息", "随机ID生成", "天气查询", "模型输出解析"], "chunk_0245_0743_4752.txt": ["messages", "sampling_params", "LoRA", "chat_template", "add_generation_prompt", "continue_final_message", "mm_processor_kwargs", "离线推理", "对话参数", "多模态处理器"], "chunk_0080_0159_9695.txt": ["异步引擎参数", "命名参数", "vllm serve", "disable-log-requests", "日志请求"], "chunk_0008_9752_9030.txt": ["ROCm", "MI200s", "MI300", "Radeon RX 7900 系列", "Linux", "Python", "GPU", "gfx90a", "gfx942", "gfx1100"], "chunk_0086_2272_5332.txt": ["视觉语言模型", "VLM", "支持的 VLM 列表", "GitHub", "issue"], "chunk_0072_7647_5332.txt": ["多模态", "模型", "输入", "扩展", "添加新模型"], "chunk_0131_4972_4829.txt": ["键", "分页注意力", "k_cache", "k_ptr", "k_vecs", "线程组", "warp", "内存布局", "BLOCK_SIZE", "HEAD_SIZE"], "chunk_0032_2546_9030.txt": ["XPU", "依赖环境", "Linux", "英特尔数据中心 GPU", "英特尔 ARC GPU WIP", "OneAPI", "oneAPI 2024.1"], "chunk_0105_5804_8233.txt": ["量化过程", "INT8", "W8A8", "SparseAutoModelForCausalLM", "校准数据", "GPTQModifier", "SmoothQuantModifier", "激活量化", "模型压缩"], "chunk_0211_8712_7601.txt": ["vllm", "LLM", "embedding", "EmbeddingRequestOutputs", "intfloat/e5-mistral-7b-instruct", "encode", "离线推理", "模型", "向量表示", "输出"], "chunk_0033_2546_2306.txt": ["Dockerfile", "XPU", "<PERSON>er", "vllm-xpu-env", "docker build", "docker run", "shm-size", "device /dev/dri", "network=host", "by-path"], "chunk_0110_1496_1012.txt": ["量化", "FP8", "W8A8", "安装", "llm-compressor", "高性能模型"], "chunk_0048_9931_7771.txt": ["分布式推理", "张量并行", "管道并行", "<PERSON>", "多进程", "GPU", "LLM 类", "API 服务器", "Megatron-LM"], "chunk_0139_1298_0591.txt": ["vllm", "离线推理", "OpenAI 服务器", "Meta-Llama-3-70B", "benchmark_serving.py", "性能分析", "<PERSON><PERSON> Profiler", "API 服务器", "sharegpt", "num-prompts"], "chunk_0223_2749_7601.txt": ["OpenAI API", "API server", "chat completion", "Los Angeles Dodgers", "World Series", "2020", "Python", "OpenAI", "API key"], "chunk_0009_9752_2811.txt": ["ROCm", "docker", "Dockerfile.rocm", "flash-attention", "GFX 架构", "MI200", "MI300", "Radeon RX 7900", "构建参数"], "chunk_0107_5804_7266.txt": ["量化", "INT8", "W8A8", "故障排除", "GitHub", "vllm-project", "llm-compressor", "issue"], "chunk_0234_4283_7601.txt": ["KServe", "Kubernetes", "部署", "分布式模型服务", "模型服务"], "chunk_0053_1121_3940.txt": ["usage_lib.py", "GCP", "NVIDIA L4", "OPTForCausalLM", "上下文", "数据收集", "GPU内存利用率", "块大小", "量化"], "chunk_0173_4039_4774.txt": ["Qwen2.5", "Infer 测试", "logits_processors", "SamplingParams", "logprobs", "token", "inference", "test samples", "DigitLogitsProcessor"], "chunk_0187_0534_5332.txt": ["<PERSON><PERSON><PERSON><PERSON>", "模型推理", "智能LLM应用程序", "分布式推理", "量化", "操作链", "教程", "开发", "高效"], "chunk_0137_8376_7601.txt": ["Dockerfile", "镜像部署", "多阶段构建", "OpenAI 兼容服务器", "dockerfilegraph", "构建图", "FROM", "COPY --from", "RUN --mount"], "chunk_0092_4092_5094.txt": ["推测解码", "MLP 推测器", "草稿模型", "上下文向量", "采样 token", "张量并行性", "HF hub", "llama3-70b-accelerator", "生成文本"], "chunk_0021_7136_1943.txt": ["VLLM_CPU_KVCACHE_SPACE", "KV 缓存", "VLLM_CPU_OMP_THREADS_BIND", "OpenMP 线程", "CPU 内核", "张量并行", "环境变量", "内存管理模式", "并行处理", "硬件配置"], "chunk_0249_0743_4752.txt": ["LLM", "离线推理", "提示参数", "sampling_params", "prompt_adapter_request", "LoRA", "批量推理", "PromptType", "tqdm", "文本生成"], "chunk_0236_2722_7601.txt": ["BentoML", "大型语言模型", "LLM", "OpenAI", "OCI", "Kubernetes", "部署", "端点", "容器化"], "chunk_0130_4972_0919.txt": ["查询数据", "线程组", "分页注意力", "全局内存", "共享内存", "q_ptr", "q_vecs", "HEAD_SIZE", "VEC_SIZE", "THREAD_GROUP_SIZE"], "chunk_0217_1952_7601.txt": ["TPU", "vllm", "离线推理", "SamplingParams", "enforce_eager", "Gemma-2b", "top-p 采样", "模型生成", "温度参数", "max_tokens"], "chunk_0189_0534_7225.txt": ["CUDA 12.1", "CUDA 11.8", "Docker 安装", "NVIDIA PyTorch Docker 映像", "<PERSON><PERSON><PERSON><PERSON>", "GPU 计算能力", "Python 3.8", "PyTorch", "langchain_community"], "chunk_0244_0743_4752.txt": ["HuggingFace Transformers", "tokenizer", "量化模型权重", "CUDA 图", "离线推理", "束搜索", "GPU 内存利用率", "模型配置文件", "SamplingParams", "LoRARequest"], "chunk_0034_2546_1698.txt": ["XPU", "intel OneAPI", "FP16", "BF16", "源代码构建", "setvars.sh", "requirements-xpu.txt", "setup.py", "数据类型"], "chunk_0096_2527_5332.txt": ["量化", "AWQ", "GPTQ", "<PERSON><PERSON>", "INT8", "FP8", "AQLM", "bitsandbytes", "硬件平台"], "chunk_0216_1333_7601.txt": ["Pixtral", "离线推理", "图像描述", "SamplingParams", "LLM", "max_tokens", "max_model_len", "image_url", "tokenizer_mode"], "chunk_0134_4972_3372.txt": ["值数据", "logits_vec", "v_vec", "点乘运算", "BLOCK_SIZE", "HEAD_SIZE", "V_VEC_SIZE", "线程", "warp", "分页注意力"], "chunk_0159_1030_7601.txt": ["NVIDIA", "AWS", "Cloudflare", "BentoML", "<PERSON><PERSON><PERSON>", "IBM 研究中心", "a16z", "交流会", "旧金山湾区"], "chunk_0050_9944_7601.txt": ["OpenAI", "metrics", "GPU", "CPU", "KV-cache", "prefix cache block hit rate", "speculative decoding", "prompt tokens", "generation tokens"], "chunk_0097_2527_0736.txt": ["Volta", "SM 7.0", "Turing", "SM 7.5", "Ampere", "SM 8.0/8.6", "Ada", "SM 8.9", "<PERSON>", "SM 9.0"], "chunk_0064_9531_1419.txt": ["第三方模型", "模型支持政策", "社区驱动", "输出合理性", "困惑度", "Transformers", "拉取请求 (PRs)", "模型测试", "运行时功能"], "chunk_0213_9784_7601.txt": ["离线推理", "MlpSpeculator", "spec decoding", "LLM", "SamplingParams", "Llama-2-13b-chat-hf", "ibm-fms/llama-13b-accelerator", "use_v2_block_manager", "生成文本"], "chunk_0081_6640_5332.txt": ["LoRA 适配器", "模型", "SQL 查询", "SamplingParams", "huggingface_hub", "lora_request", "异步引擎", "enable_lora", "table schema"], "chunk_0047_9931_7394.txt": ["分布式推理", "张量并行", "管道并行", "GPU 数量", "节点数量", "GPU blocks", "吞吐量", "模型大小", "单节点多 GPU"], "chunk_0060_1405_3977.txt": ["float32", "float16", "bfloat16", "温度参数", "请求种子", "稳定", "内存使用量", "方差", "精度差异", "生成效果"], "chunk_0017_7136_5332.txt": ["x86 CPU 平台", "FP32", "BF16", "Dockerfile", "源代码构建", "环境变量", "PyTorch 的英特尔扩展", "性能提示", "模型推理"], "chunk_0241_2521_7601.txt": ["llama_index", "Vllm 类", "GPU 推理", "Orca-2-7b", "tensor_parallel_size", "max_new_tokens", "swap_space", "gpu_memory_utilization", "llama_index.llms.vllm"], "chunk_0109_1496_9007.txt": ["FP8", "动态量化", "在线量化", "模型权重", "精度", "激活值", "张量尺度", "延迟改善", "校准数据"], "chunk_0119_0507_5473.txt": ["APC", "长文档查询", "KV 缓存", "多轮对话", "吞吐量", "延迟", "工作负载", "重用处理结果", "自动前缀缓存"], "chunk_0181_1478_2014.txt": ["AWQ", "Qwen2.5-3B-Instruct-AWQ", "OOM 错误", "max_num_seqs", "max_model_len", "tensor_parallel_size", "gpu_memory_utilization", "LLM", "tokenizer"], "chunk_0225_1930_7601.txt": ["OpenAI API", "completion客户端", "API server", "模型", "logprobs", "prompt", "stream", "API key", "base_url"], "chunk_0182_1478_9702.txt": ["AWQ量化", "Qwen2.5-3B-Instruct", "少样本学习", "后处理数据", "wide_to_long", "DataFrame", "AnswerId", "MisconceptionId", "ConstructName"], "chunk_0135_4972_3649.txt": ["warp", "accs", "归约", "线程", "共享内存", "HEAD_SIZE", "NUM_ROWS_PER_THREAD", "NUM_WARPS", "VLLM_SHFL_XOR_SYNC", "分页注意力"], "chunk_0022_7136_2633.txt": ["PyTorch", "英特尔扩展", "IPEX", "性能提升", "Intel 硬件", "特性优化"], "chunk_0199_1279_7601.txt": ["GGUF", "vllm", "TinyLlama", "SamplingParams", "LLM", "推理", "huggingface_hub", "模型路径", "生成文本", "GPU内存利用率"], "chunk_0094_4532_1811.txt": ["Transformer 架构", "KV 缓存", "抢占", "端到端性能", "gpu_memory_utilization", "max_num_seqs", "max_num_batched_tokens", "tensor_parallel_size", "Prometheus 指标"], "chunk_0088_2272_5835.txt": ["视觉语言模型", "OpenAI Vision API", "Phi-3.5-vision-instruct", "在线推理", "HTTP 服务器", "聊天模板", "多图像输入推理", "HuggingFace", "API 服务器"], "chunk_0177_1478_5332.txt": ["AWQ 量化", "Qwen2.5-3B-Instruct", "少样本学习", "BGE 嵌入", "Borda <PERSON>", "支持问题检索", "对话生成", "误解推断", "chat（） 功能"], "chunk_0063_9531_0425.txt": ["多模态语言模型", "文本生成", "多模态 Embedding", "LLaVA", "InternVL2", "MiniCPM-V", "Phi-3-Vision", "Qwen-VL", "LoRA"], "chunk_0166_7177_0299.txt": ["API 地址", "OpenAI 客户端", "Curl 命令", "模型", "Completion API", "OpenBayes 平台", "localhost:8080", "<PERSON>wen-1_8B-<PERSON><PERSON>", "max_tokens"], "chunk_0214_8403_7601.txt": ["XLA HLO 图", "NEURON_CONTEXT_LENGTH_BUCKETS", "NEURON_TOKEN_GEN_BUCKETS", "transformers-neuronx", "AWS Neuron SDK", "TinyLlama-1.1B-Chat-v1.0", "离线推理", "max_model_len", "block_size"], "chunk_0001_4386_8860.txt": ["PagedAttention", "大型语言模型", "CUDA/HIP 图", "量化", "GPTQ", "AWQ", "连续批处理", "分布式推理", "OpenAI API 服务器"], "chunk_0169_4039_6099.txt": ["Qwen2.5", "OpenBayes 云平台", "安装", "pip install vllm", "推理", "本地部署", "更新", "vllm==0.5.4"], "chunk_0005_4287_1396.txt": ["pip 安装", "CUDA 12.1", "CUDA 11.8", "conda 环境", "PyTorch", "二进制文件", "wheel", "编译", "CUDA 版本"], "chunk_0221_2419_7601.txt": ["离线推理", "Torch分析器", "SamplingParams", "LLM", "模型生成", "提示文本", "性能分析", "OPT-125m", "生成文本"], "chunk_0265_5679_7601.txt": ["LLMEngine", "AsyncLLMEngine", "输入处理管道", "INPUT_REGISTRY.process_input", "多模态数据", "KV缓存", "ModelRunnerBase", "ExecutorBase", "WorkerBase", "MULTIMODAL_REGISTRY.map_input"], "chunk_0118_0507_5120.txt": ["APC", "enable_prefix_caching", "KV缓存", "SamplingParams", "LLM", "Markdown表格", "生成时间", "模型", "KV cache"], "chunk_0161_7177_5332.txt": ["配置", "运行", "入门教程", "安装", "启动", "在线运行", "分步指南", "openbayes.com", "proper nouns"], "chunk_0184_1478_4131.txt": ["llm.chat", "AWQ量化", "Qwen2.5-3B-Instruct", "少样本学习", "SamplingParams", "temperature", "n", "MisconceptionId", "QuestionId"], "chunk_0237_0670_7601.txt": ["Cerebrium", "GPU 计算机", "无服务器 AI 基础设施平台", "部署", "LLM 推理", "Mistral-7B-Instruct-v0.1", "cerebrium.toml", "main.py", "自动扩充端点"], "chunk_0124_4708_5849.txt": ["性能基准测试", "nightly 基准测试", "PR", "触发基准测试", "基准测试环境", "工作负载", "指标", "perf-benchmarks", "nightly-benchmarks"], "chunk_0247_0743_4752.txt": ["prompts", "语言模型", "池化参数", "LoRA 请求", "提示适配器请求", "批量推理", "PromptType", "离线推理", "vllm", "use_tqdm"], "chunk_0044_6133_5597.txt": ["chat completion API", "命名函数调用", "工具调用", "配置文件", "yaml", "serve 模块", "Outlines", "tool_choice", "函数解析"], "chunk_0036_1188_0435.txt": ["离线批量推理", "LLM", "SamplingParams", "OPT-125M", "采样温度", "核采样概率", "离线推理", "RequestOutput", "高吞吐量"], "chunk_0163_7177_8647.txt": ["CUDA 12.1", "OpenBayes 云平台", "pip install vllm", "nvcc --version"], "chunk_0027_0142_5332.txt": ["PyTorch XLA", "Google Cloud TPU", "TPU", "安装"], "chunk_0188_0534_7064.txt": ["<PERSON><PERSON><PERSON><PERSON>", "多 GPU 推理", "量化", "链"], "chunk_0138_1298_5332.txt": ["torch.profiler", "VLLM_TORCH_PROFILER_DIR", "性能分析", "benchmarks/benchmark_serving.py", "Llama 70B", "H100", "跟踪日志", "perfetto.dev", "VLLM_RPC_TIMEOUT"], "chunk_0079_0159_1272.txt": ["Huggingface 模型", "tokenizer", "量化", "LoRA 适配器", "CUDA 图", "多模态处理", "推测解码", "并行配置", "模型加载格式", "调度策略"], "chunk_0023_7136_1861.txt": ["TCMalloc", "CPU 核心", "OpenMP", "缓存局部性", "动态链接库路径", "超线程", "NUMA", "内存分配", "LD_PRELOAD"], "chunk_0041_6133_7161.txt": ["OpenAI API", "Chat API", "Completions API", "guided decoding", "guided_choice", "guided_json", "guided_regex", "guided_grammar", "RAG"], "chunk_0042_6133_1382.txt": ["聊天模板", "tokenizer 配置", "Jinja2 模板", "指令微调", "模型服务", "OpenAI 兼容服务器", "NousResearch/Meta-Llama-3-8B-Instruct", "示例目录", "chat-template 参数"], "chunk_0061_9531_5332.txt": ["HuggingFace Transformers", "生成性 Transformer 模型", "模型架构", "流行模型"], "chunk_0020_7136_1698.txt": ["源代码构建", "CPU 后端", "oneDNN", "gcc/g++", "AVX512_BF16", "BF16", "编译器", "Python 包", "ISA"], "chunk_0186_1478_6711.txt": ["AWQ", "Qwen2.5-3B-Instruct", "少样本学习", "MisconceptionId", "QuestionId_Answer", "apk@25", "submission.csv", "量化", "调试"], "chunk_0208_3407_7601.txt": ["离线推理", "音频语言模型", "Ultravox 0.3", "HuggingFace", "SamplingParams", "AudioAsset", "tokenizer", "multi_modal_data", "batch inference"], "chunk_0185_1478_4140.txt": ["AWQ 量化", "Qwen2.5-3B-Instruct", "少样本学习", "嵌入模型", "generate_embeddings", "cosine_similarity", "Borda count", "MisconceptionId", "CLs token"], "chunk_0232_1686_3481.txt": ["SkyPilot", "Llama-3", "GPU", "A100", "Gradio", "模型部署", "加速器", "8B 模型", "70B 模型"], "chunk_0191_0534_5292.txt": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "PromptTemplate", "链", "集成", "灵活性", "模板", "操作链", "交互"], "chunk_0162_7177_7064.txt": ["安装", "模型准备", "离线推理", "服务器", "参数设置", "命令行", "OpenAI 客户端", "Curl 命令", "请求"], "chunk_0108_1496_5332.txt": ["FP8", "W8A8", "Nvidia H100", "AMD MI300x", "E4M3", "E5M2", "量化", "Marlin 内核", "计算能力"], "chunk_0068_9128_7002.txt": ["forward 方法", "PagedAttention", "AttentionMetadata", "输入参数", "多头注意力机制", "旋转位置嵌入", "模型架构", "张量", " SamplerOutput"], "chunk_0212_6668_7601.txt": ["BART", "encoder/decoder", "TextPrompt", "TokensPrompt", "ExplicitEncoderDecoderPrompt", "离线推理", "SamplingParams", "模型提示", "文本生成"], "chunk_0268_3267_0724.txt": ["多模态", "插件", "开发者文档", "添加", "指南"], "chunk_0089_4092_5332.txt": ["推测解码", "token 间延迟", "LLM 推理", "采样参数", "优化工作", "内存密集型"], "chunk_0071_9128_9215.txt": ["树外模型集成", "ModelRegistry", "多模态模型", "CUDA", "vllmserve", "API 服务器", "延迟加载", "SupportsMultiModal", "模型注册"], "chunk_0102_1138_7601.txt": ["GGUF", "TinyLlama-1.1B-Chat-v1.0", "tokenizer", "内存占用", "单文件模型", "gguf-split", "张量并行推理", "SamplingParams", "LLM"], "chunk_0016_2376_7101.txt": ["LoRA 服务", "LLM 模型", "LLaVa", "编码器-解码器模型", "vLLM OpenVINO 集成", "张量并行", "管道并行"], "chunk_0168_4039_7064.txt": ["Qwen2.5", "量化模型", "提示工程", "Infer测试", "推理概率", "CSV", "CV分数"], "chunk_0183_1478_2412.txt": ["AWQ 量化", "Qwen2.5-3B-Instruct", "少样本学习", "辅助函数", "相似问题", "ConstructName", "SubjectName", "QuestionId", "聊天对话"], "chunk_0190_0534_0405.txt": ["<PERSON><PERSON><PERSON><PERSON>", "Qwen2.5-1.5B-Instruct", "Hugging Face", "top_k", "top_p", "temperature", "max_new_tokens", "模型配置", "信任远程代码"], "chunk_0104_5804_5560.txt": ["INT8 量化", "llm-compressor", "pip install", "依赖"], "chunk_0101_5968_0999.txt": ["量化", "BitsAndBytes", "4 位量化", "LLM", "load_format", "huggyllama/llama-7b", "torch.bfloat16", "trust_remote_code", "模型加载"], "chunk_0239_6874_7601.txt": ["dstack", "GPU 计算机", "开源框架", "serve.dstack.yml", "MODEL=NousResearch/Llama-2-7b-chat-hf", "OpenAI SDK", "递归", "云部署", "Service"], "chunk_0243_0743_5332.txt": ["vllm.LLM", "大语言模型", "tokenizer", "KV缓存", "GPU内存", "智能批处理", "离线推理", "采样参数", "模型生成", "语言模型"], "chunk_0267_3267_5332.txt": ["多模态模型", "vllm.multimodal", "PromptInputs", "multi_modal_data", "图像数据", "多模态输入", "支持的模型"], "chunk_0198_8557_7601.txt": ["CPU 离线处理", "vllm", "LLM", "SamplingParams", "模型推理", "Llama-2-13b-chat-hf", "生成文本", "RequestOutput"], "chunk_0106_5804_7288.txt": ["量化", "INT8", "W8A8", "校准数据", "序列长度", "聊天模板", "指令模板", "微调", "训练数据"], "chunk_0174_4039_0921.txt": ["Qwen2.5", "推理概率", "logprobs", "inference errors", "np.array", "math.exp", "logprob", "KEEP", "responses"], "chunk_0206_5484_7601.txt": ["vllm", "LLM", "SamplingParams", "离线推理", "API Client", "facebook/opt-125m", "RequestOutput", "生成文本", "提示示例", "模型"], "chunk_0126_7585_4752.txt": ["采样参数", "输出序列", "presence_penalty", "frequency_penalty", "repetition_penalty", "temperature", "top_p", "top_k", "stop_token_ids", "logprobs"], "chunk_0180_1478_7621.txt": ["AWQ", "Qwen2.5-3B-Instruct", "少样本学习", "数据加载", "eedi-mining-misconceptions-in-mathematics", "KAGGLE_IS_COMPETITION_RERUN", "train.csv", "test.csv", "misconception_mapping.csv"], "chunk_0093_4092_3603.txt": ["推测解码", "预取调度", "动态推测解码", "批量扩展", "黑客指南", "贡献者资源"], "chunk_0207_9824_7601.txt": ["离线推理", "Arctic", "SamplingParams", "LLM", "snowflake-arctic-instruct", "deepspeedfp", "tensor_parallel_size", "生成文本", "RequestOutput"], "chunk_0098_9677_7601.txt": ["AutoAWQ", "AWQ", "量化", "INT4", "FP16", "模型", "内存占用", "延迟", "Huggingface"], "chunk_0235_2990_7601.txt": ["Triton 推理服务器", "NVIDIA Triton", "facebook/opt-125m", "部署", "模型", "集成", "服务"], "chunk_0002_4386_0317.txt": ["索引", "模块索引", "Python", "表格"], "chunk_0250_0743_0257.txt": ["RequestOutput", "离线推理", "LLM", "返回概述", "prompts", "prompt_token_ids", "inputs", "补全内容"], "chunk_0040_6133_3269.txt": ["API 参考", "OpenAI API", "Vision API", "工具", "tool_choice", "suffix", "兼容推理", "OpenAI 兼容服务器"], "chunk_0057_4165_0793.txt": ["CP", "APC", "LoRA", "CUDA graph", "enc-dec", "logP", "async output", "multi-step", "MM", "beam-search"], "chunk_0010_9752_8768.txt": ["ROCm", "Triton flash attention", "CK flash-attention", "PyTorch", "hipBLAS", "MI210", "MI250", "MI300x", "gfx942"], "chunk_0062_9531_7074.txt": ["<PERSON><PERSON><PERSON>", "Mixtral", "<PERSON><PERSON>", "LoRA", "PP", "<PERSON>", "文本生成", "文本 Embedding", "奖励模型"], "chunk_0238_8030_7601.txt": ["LeaderWorkerSet", "LWS", "Kubernetes", "AI/ML 推理", "分布式推理", "部署模式", "分布式模型服务"], "chunk_0065_9128_5332.txt": ["HuggingFace Transformers", "模型架构", "注意力机制", "多模态输入", "集成", "GitHub 存储库", "新运算符"], "chunk_0127_4972_5332.txt": ["分页键值缓存", "多头查询注意力内核", "共享内存", "全局内存", "内存布局", "线程块", "块", "csrc/attention/attention_kernels.cu", "点乘实现"], "chunk_0099_5968_5332.txt": ["BitsAndBytes", "量化模型", "内存使用", "性能增强", "动态量化", "预量化 checkpoint", "quantization_config", "模型推理", "huggingface"], "chunk_0264_7268_8612.txt": ["InputRegistry", "DummyDataFactory", "InputContext", "LLMInputs", "TokenInputs", "ModelConfig", "HuggingFace", "MultiModalRegistry", "register_input_processor", "process_input"], "chunk_0178_1478_7064.txt": ["AWQ 量化", "Qwen2.5-3B-Instruct", "少样本学习", "后处理数据", "llm.chat", "最相似的误解"], "chunk_0230_1686_5332.txt": ["SkyPilot", "云", "Kubernetes", "LLM", "Llama-3", "Mixtral", "AI gallery", "服务副本", "开源框架"], "chunk_0083_6640_1163.txt": ["LoRA 适配器", "动态加载", "动态卸载", "vLLM 服务器", "API 端点", "运行时", "环境变量", "模型适配器", "POST 请求", "lora_name"], "chunk_0075_7647_5541.txt": ["多模态输入", "token 最大数量", "INPUT_REGISTRY.register_dummy_data", "MULTIMODAL_REGISTRY.register_image_input_mapper", "图像输入", "静态特征尺寸", "动态特征尺寸", "LLaVA-1.5 模型", "LLaVA-NeXT 模型", "输入处理管道"], "chunk_0082_6640_0375.txt": ["LoRA 适配器", "vLLM 服务器", "模型服务", "sql-lora", "max_loras", "适配模型", "Open-AI 兼容", "lora-modules", "基础模型", "max_lora_rank"], "chunk_0197_4776_7601.txt": ["AQLM", "Llama-2-7b", "Llama-2-13b", "Mixtral-8x7b", "TinyLlama-1_1B", "2Bit", "HF", "SamplingParams", "模型量化"], "chunk_0242_1487_7601.txt": ["SkyPilot", "KServe", "NVIDIA Triton", "BentoML", "Cerebrium", "LWS", "dstack", "Langchain", "llama_index"], "chunk_0070_9128_3094.txt": ["注册模型", "ForCausalLM", "vllm", "_MODELS", "model_executor", "init.py"], "chunk_0176_4039_3260.txt": ["Qwen2.5", "CV 分数", "log_loss", "sklearn.metrics", "推理", "验证", "结果评估"], "chunk_0066_9128_1001.txt": ["Fork vLLM 存储库", "GitHub", "源代码构建", "模型集成", "代码库"], "chunk_0067_9128_9477.txt": ["HuggingFace Transformers", "PyTorch 模型代码", "vllm/model_executor/models", "OPT 模型", "modeling_opt.py", "版权", "许可条款"], "chunk_0019_7136_2306.txt": ["Dockerfile", "CPU", "docker build", "docker run", "vllm-cpu-env", "--shm-size", "--cpuset-cpus", "--cpuset-mems", "--network=host", "安装"], "chunk_0204_6255_7601.txt": ["MultiLoRA", "LoRA", "Llama2", "HuggingFace", "SQL", "离线推理", "SamplingParams", "LoRARequest", "LLMEngine"], "chunk_0116_3001_7601.txt": ["FP8", "E4M3", "KV缓存", "量化", "缩放因子", "LLM推理", "OCP", "AMD MI300", "NVIDIA Hopper", "AMMO"], "chunk_0257_7311_4971.txt": ["LLMEngine", "SamplingParams", "解码配置", "LoRA配置", "模型配置", "并行配置", "调度程序配置", "未完成请求", "step函数", "请求处理"], "chunk_0035_1188_5332.txt": ["离线批量推理", "大语言模型", "API 服务器", "OpenAI 兼容", "HuggingFace", "ModelScope", "安装说明", "环境变量", "VLLM_USE_MODELSCOPE"], "chunk_0261_1558_7601.txt": ["LLMEngine", "AsyncLLMEngine", "vllm", "引擎", "开发者文档"], "chunk_0226_1549_7601.txt": ["OpenAI API", "嵌入客户端", "模型", "API 服务"], "chunk_0077_7647_1426.txt": ["输入处理器", "LLMEngine", "多模态嵌入", "INPUT_REGISTRY", "register_input_processor", "占位符 tokens", "注意力掩码", "vLLM 框架", "LLaVA-1.5模型", "LLaVA-NeXT 模型"], "chunk_0224_6689_7601.txt": ["OpenAI", "工具调用", "chat completion", "Mistral-7B-Instruct-v0.3", "Hermes-2-Pro-Llama-3-8B", "API 服务器", "tool call parser", "chat template", "get_current_weather"], "chunk_0084_6640_4874.txt": ["LoRA 模块", "–lora-modules", "JSON 格式", "base_model_name", "键值对", "适配器", "模型路径", "Llama-2-7b", "yard1", "向后兼容性"], "chunk_0112_1496_7266.txt": ["量化", "FP8", "W8A8", "故障排除", "GitHub", "issue", "vllm-project", "llm-compressor", "支持"], "chunk_0029_0142_5510.txt": ["Dockerfile.tpu", "Docker 镜像", "TPU", "docker build", "docker run", "--privileged", "--net host", "--shm-size=16G", "vllm-tpu", "安装"], "chunk_0055_7685_7601.txt": ["CoreWeave's <PERSON><PERSON><PERSON>", "模型张量", "序列化", "GPU", "Pod 启动时间", "S3 端点", "HTTP/HTTPS 端点", "反序列化", "张量加密"], "chunk_0219_2551_7601.txt": ["视觉语言模型", "离线推理", "多图像输入", "Phi-3.5-vision-instruct", "图像占位符", "聊天模板", "图像URL", "模型定义", "视觉推理"], "chunk_0215_9782_7601.txt": ["Neuron", "int8量化", "XLA HLO图", "LLM", "TinyLlama-1.1B-Chat-v1.0", "block_size", "max_model_len", "transformers-neuronx", "AWS Neuron SDK", "离线推理"], "chunk_0231_1686_5560.txt": ["SkyPilot", "HuggingFace", "Meta-Llama-3-8B-Instruct", "依赖", "云服务", "Kubernetes", "pip install skypilot-nightly", "sky check"], "chunk_0171_4039_1727.txt": ["Qwen2.5", "测试数据", "train", "CV 分数", "pandas", "read_csv", "数据加载"], "chunk_0175_4039_7226.txt": ["Qwen2.5", "CSV", "pd.read_csv", "submission.csv", "winner_model_a", "winner_model_b", "winner_tie", "to_csv", "测试数据"], "chunk_0260_1989_7601.txt": ["AsyncLLMEngine", "LLMEngine", "异步", "engine_step", "generate", "encode", "RequestTracker", "AsyncStream", "LoRA", "PoolingParams"], "chunk_0160_6329_7601.txt": ["社区项目", "计算资源", "赞助商", "OpenCollective", "AMD", "AWS", "NVIDIA", "UC Berkeley", "推广应用"]}