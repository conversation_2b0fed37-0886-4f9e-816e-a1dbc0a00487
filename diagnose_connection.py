"""
ES连接诊断工具
"""

import requests
import socket
from urllib.parse import urlparse

from es_client import load_es_config_from_env, ElasticsearchClient


def test_network_connectivity(host: str, port: int, timeout: int = 5) -> bool:
    """测试网络连通性"""
    try:
        sock = socket.create_connection((host, port), timeout)
        sock.close()
        return True
    except Exception as e:
        print(f"   ❌ 网络连接失败: {str(e)}")
        return False


def test_http_request(url: str, username: str = None, password: str = None, timeout: int = 10):
    """测试HTTP请求"""
    try:
        auth = None
        if username and password:
            auth = (username, password)
        
        response = requests.get(url, auth=auth, timeout=timeout, verify=False)
        print(f"   ✅ HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   ✅ 集群名称: {data.get('cluster_name', 'unknown')}")
                print(f"   ✅ ES版本: {data.get('version', {}).get('number', 'unknown')}")
                return True
            except:
                print("   ⚠️  响应不是有效的JSON")
                return False
        elif response.status_code == 401:
            print("   ❌ 认证失败，请检查用户名和密码")
            return False
        else:
            print(f"   ❌ HTTP请求失败: {response.text[:200]}")
            return False
            
    except requests.exceptions.ConnectTimeout:
        print("   ❌ 连接超时")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"   ❌ 连接错误: {str(e)}")
        return False
    except Exception as e:
        print(f"   ❌ HTTP请求异常: {str(e)}")
        return False


def main():
    print("🔍 Elasticsearch连接诊断工具")
    print("=" * 50)
    
    # 加载配置
    print("1. 加载配置...")
    config = load_es_config_from_env()
    print(f"   📋 ES主机: {config.hosts}")
    print(f"   👤 用户名: {config.username or '未设置'}")
    print(f"   🔐 密码: {'已设置' if config.password else '未设置'}")
    print(f"   ⏱️  超时时间: {config.timeout}秒")
    
    # 测试每个主机
    for i, host_url in enumerate(config.hosts, 1):
        print(f"\n2.{i} 测试主机: {host_url}")
        
        # 解析URL
        parsed = urlparse(host_url)
        host = parsed.hostname
        port = parsed.port or 9200
        
        print(f"   🔗 主机地址: {host}:{port}")
        
        # 测试网络连通性
        print("   🌐 测试网络连通性...")
        if test_network_connectivity(host, port):
            print("   ✅ 网络连接正常")
        else:
            print("   ❌ 网络连接失败，跳过HTTP测试")
            continue
        
        # 测试HTTP请求
        print("   📡 测试HTTP请求...")
        if test_http_request(host_url, config.username, config.password):
            print("   ✅ HTTP请求成功")
        else:
            print("   ❌ HTTP请求失败")
    
    # 使用ES客户端测试
    print(f"\n3. 使用ES客户端测试...")
    es_client = ElasticsearchClient(config)
    
    print("   🔍 执行ping测试...")
    ping_result = es_client.ping()
    if ping_result.success:
        print("   ✅ ES客户端ping成功")
    else:
        print(f"   ❌ ES客户端ping失败: {ping_result.error}")
    
    print("   🔍 执行详细连接测试...")
    detailed_result = es_client.test_connection_detailed()
    if detailed_result.success:
        print("   ✅ 详细连接测试成功")
    else:
        print(f"   ❌ 详细连接测试失败: {detailed_result.error}")
    
    # 尝试获取集群信息
    print("   🔍 尝试获取集群信息...")
    info_result = es_client.get_cluster_info()
    if info_result.success:
        print(f"   ✅ 集群信息获取成功: {info_result.message}")
    else:
        print(f"   ❌ 集群信息获取失败: {info_result.error}")
    
    # 关闭连接
    es_client.close()
    
    print(f"\n🎯 诊断总结:")
    print("如果网络连接正常但ES客户端失败，可能的原因：")
    print("1. 认证信息错误（用户名/密码）")
    print("2. ES版本兼容性问题")
    print("3. SSL/TLS配置问题")
    print("4. ES客户端库版本问题")
    print("5. 防火墙或安全组配置")


if __name__ == "__main__":
    main()
