"""
演示脚本 - 不依赖Elasticsearch服务
展示数据处理和索引设计功能
"""

import json
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

from loguru import logger
from keyword_index_manager import KeywordDocument


def analyze_data_file(file_path: str) -> Dict[str, Any]:
    """分析数据文件"""
    print(f"📊 分析数据文件: {file_path}")
    
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 基本统计
        total_chunks = len(data)
        all_keywords = []
        keyword_counts = []
        
        for chunk_id, keywords in data.items():
            all_keywords.extend(keywords)
            keyword_counts.append(len(keywords))
        
        total_keywords = len(all_keywords)
        unique_keywords = len(set(all_keywords))
        avg_keywords = total_keywords / total_chunks if total_chunks > 0 else 0
        max_keywords = max(keyword_counts) if keyword_counts else 0
        min_keywords = min(keyword_counts) if keyword_counts else 0
        
        # 关键词频率统计
        keyword_freq = {}
        for keyword in all_keywords:
            keyword_freq[keyword] = keyword_freq.get(keyword, 0) + 1
        
        # 排序获取热门关键词
        top_keywords = sorted(keyword_freq.items(), key=lambda x: x[1], reverse=True)[:20]
        
        stats = {
            "total_chunks": total_chunks,
            "total_keywords": total_keywords,
            "unique_keywords": unique_keywords,
            "avg_keywords": round(avg_keywords, 2),
            "max_keywords": max_keywords,
            "min_keywords": min_keywords,
            "top_keywords": top_keywords,
            "keyword_freq": keyword_freq
        }
        
        print(f"✅ 数据分析完成:")
        print(f"   📄 文档块数量: {total_chunks}")
        print(f"   🔤 总关键词数: {total_keywords}")
        print(f"   🎯 唯一关键词数: {unique_keywords}")
        print(f"   📊 平均关键词数: {avg_keywords}")
        print(f"   📈 最大关键词数: {max_keywords}")
        print(f"   📉 最小关键词数: {min_keywords}")
        
        return stats
        
    except Exception as e:
        print(f"❌ 数据分析失败: {str(e)}")
        return {}


def show_top_keywords(stats: Dict[str, Any], limit: int = 15):
    """显示热门关键词"""
    print(f"\n🔥 热门关键词 (Top {limit}):")
    print("-" * 50)
    
    for i, (keyword, count) in enumerate(stats["top_keywords"][:limit], 1):
        print(f"{i:2d}. {keyword:<20} ({count:3d} 次)")


def show_sample_documents(file_path: str, limit: int = 5):
    """显示示例文档"""
    print(f"\n📋 示例文档 (前 {limit} 个):")
    print("-" * 80)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        for i, (chunk_id, keywords) in enumerate(list(data.items())[:limit], 1):
            print(f"\n{i}. 文档块ID: {chunk_id}")
            print(f"   关键词数量: {len(keywords)}")
            print(f"   关键词: {', '.join(keywords[:8])}{'...' if len(keywords) > 8 else ''}")
            
    except Exception as e:
        print(f"❌ 读取示例文档失败: {str(e)}")


def demonstrate_index_mapping():
    """演示索引映射设计"""
    print(f"\n🗂️  索引映射设计:")
    print("-" * 50)
    
    mapping = {
        "properties": {
            "chunk_id": {
                "type": "keyword",
                "fields": {
                    "text": {
                        "type": "text",
                        "analyzer": "standard"
                    }
                }
            },
            "keywords": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart",
                "fields": {
                    "keyword": {
                        "type": "keyword"
                    },
                    "suggest": {
                        "type": "completion",
                        "analyzer": "simple"
                    }
                }
            },
            "keyword_count": {
                "type": "integer"
            },
            "created_at": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
            },
            "updated_at": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
            }
        }
    }
    
    print("✅ 索引映射特点:")
    print("   🔍 chunk_id: keyword类型，支持精确匹配和文本搜索")
    print("   📝 keywords: 多字段设计，支持中文分词、精确匹配、自动补全")
    print("   📊 keyword_count: 整数类型，支持范围查询和聚合")
    print("   📅 时间字段: 支持多种日期格式")
    
    print(f"\n🔧 索引设置:")
    print("   📦 分片数: 1 (适合中小型数据集)")
    print("   🔄 副本数: 1 (提供高可用性)")
    print("   🇨🇳 中文分词: ik_max_word/ik_smart")
    print("   📏 最大结果窗口: 50000")


def demonstrate_search_queries():
    """演示搜索查询设计"""
    print(f"\n🔍 搜索查询设计:")
    print("-" * 50)
    
    # 关键词搜索查询
    keyword_query = {
        "bool": {
            "should": [
                {
                    "term": {
                        "keywords.keyword": {
                            "value": "量化",
                            "boost": 3.0
                        }
                    }
                },
                {
                    "match": {
                        "keywords": {
                            "query": "量化",
                            "boost": 2.0
                        }
                    }
                },
                {
                    "wildcard": {
                        "keywords.keyword": {
                            "value": "*量化*",
                            "boost": 1.0
                        }
                    }
                }
            ],
            "minimum_should_match": 1
        }
    }
    
    print("✅ 关键词搜索查询:")
    print("   🎯 精确匹配 (boost: 3.0)")
    print("   📝 文本匹配 (boost: 2.0)")
    print("   🔍 通配符匹配 (boost: 1.0)")
    
    # 多关键词搜索
    multi_keyword_query = {
        "bool": {
            "must": [
                {
                    "bool": {
                        "should": [
                            {"term": {"keywords.keyword": "量化"}},
                            {"match": {"keywords": "量化"}}
                        ]
                    }
                },
                {
                    "bool": {
                        "should": [
                            {"term": {"keywords.keyword": "模型"}},
                            {"match": {"keywords": "模型"}}
                        ]
                    }
                }
            ]
        }
    }
    
    print("\n✅ 多关键词搜索 (AND操作):")
    print("   🔗 每个关键词都必须匹配")
    print("   ⚖️  支持精确和模糊匹配组合")
    
    # 聚合查询
    aggregation_query = {
        "keyword_count_stats": {
            "stats": {
                "field": "keyword_count"
            }
        },
        "top_keywords": {
            "terms": {
                "field": "keywords.keyword",
                "size": 20
            }
        },
        "keyword_count_distribution": {
            "histogram": {
                "field": "keyword_count",
                "interval": 5
            }
        }
    }
    
    print("\n✅ 聚合查询:")
    print("   📊 关键词数量统计")
    print("   🔥 热门关键词排行")
    print("   📈 关键词数量分布")


def simulate_search_results(file_path: str, search_keyword: str):
    """模拟搜索结果"""
    print(f"\n🔍 模拟搜索: '{search_keyword}'")
    print("-" * 50)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 模拟搜索匹配
        matches = []
        for chunk_id, keywords in data.items():
            score = 0
            matched_keywords = []
            
            for keyword in keywords:
                if search_keyword in keyword:
                    if keyword == search_keyword:
                        score += 3.0  # 精确匹配
                    else:
                        score += 1.0  # 部分匹配
                    matched_keywords.append(keyword)
            
            if score > 0:
                matches.append({
                    "chunk_id": chunk_id,
                    "score": score,
                    "matched_keywords": matched_keywords,
                    "total_keywords": len(keywords),
                    "keywords": keywords
                })
        
        # 按评分排序
        matches.sort(key=lambda x: x["score"], reverse=True)
        
        print(f"✅ 找到 {len(matches)} 条匹配结果")
        
        # 显示前5个结果
        for i, match in enumerate(matches[:5], 1):
            print(f"\n{i}. 文档块: {match['chunk_id']}")
            print(f"   评分: {match['score']:.1f}")
            print(f"   匹配关键词: {', '.join(match['matched_keywords'])}")
            print(f"   总关键词数: {match['total_keywords']}")
            
    except Exception as e:
        print(f"❌ 模拟搜索失败: {str(e)}")


def main():
    """主演示函数"""
    print("🚀 Elasticsearch关键词搜索系统 - 功能演示")
    print("=" * 80)
    
    data_file = "filtered_keywords-Copy3.json"
    
    # 1. 数据分析
    stats = analyze_data_file(data_file)
    
    if not stats:
        print("❌ 无法继续演示，请确保数据文件存在")
        return
    
    # 2. 显示热门关键词
    show_top_keywords(stats)
    
    # 3. 显示示例文档
    show_sample_documents(data_file)
    
    # 4. 演示索引设计
    demonstrate_index_mapping()
    
    # 5. 演示查询设计
    demonstrate_search_queries()
    
    # 6. 模拟搜索
    test_keywords = ["量化", "模型", "GPU", "Python"]
    for keyword in test_keywords:
        simulate_search_results(data_file, keyword)
    
    print(f"\n🎉 演示完成!")
    print("=" * 80)
    print("📝 总结:")
    print("✅ 完整的Elasticsearch客户端模块")
    print("✅ 优化的索引映射和设置")
    print("✅ 丰富的搜索功能设计")
    print("✅ 详细的统计分析功能")
    print("✅ 支持中文分词和多种匹配方式")
    
    print(f"\n🔧 使用说明:")
    print("1. 启动Elasticsearch服务")
    print("2. 配置.env文件中的ES连接信息")
    print("3. 运行 python main.py 开始使用")
    print("4. 或运行 python test_es_functionality.py 进行功能测试")


if __name__ == "__main__":
    main()
