# Elasticsearch关键词搜索系统

一个基于Python和Elasticsearch的完整关键词索引和搜索系统，支持单机、集群模式，代理访问，以及丰富的搜索功能。

## 功能特性

### 🔧 Elasticsearch客户端功能
- ✅ 支持单机和集群模式
- ✅ 支持HTTP/SOCKS代理访问
- ✅ 完整的增删改查操作
- ✅ 批量操作支持
- ✅ 异步操作支持
- ✅ 连接状态监控
- ✅ 详细的日志记录

### 🔍 搜索功能
- ✅ 关键词精确搜索
- ✅ 关键词模糊搜索
- ✅ 多关键词组合搜索(AND/OR)
- ✅ 文档块ID搜索
- ✅ 相似文档推荐
- ✅ 搜索结果高亮
- ✅ 分页和排序
- ✅ 关键词建议

### 📊 统计分析
- ✅ 关键词统计信息
- ✅ 文档数量统计
- ✅ 热门关键词排行
- ✅ 关键词分布分析
- ✅ 聚合查询支持

## 安装和配置

### 1. 环境要求
- Python 3.8+
- Elasticsearch 7.x/8.x
- uv (Python包管理器)

### 2. 安装依赖
```bash
# 使用uv安装依赖
uv add elasticsearch[async] python-dotenv loguru pydantic typing-extensions
```

### 3. 配置Elasticsearch
复制配置文件并修改：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# 基本配置
ES_HOSTS=localhost:9200
ES_USERNAME=elastic
ES_PASSWORD=your_password
ES_TIMEOUT=30

# 集群配置示例
# ES_HOSTS=node1:9200,node2:9200,node3:9200

# 代理配置（可选）
# ES_PROXY_HOST=proxy.example.com
# ES_PROXY_PORT=8080
```

### 4. 准备数据
确保 `filtered_keywords-Copy3.json` 文件在项目根目录。

## 使用方法

### 1. 运行测试
```bash
python test_es_functionality.py
```

### 2. 启动主程序
```bash
python main.py
```

### 3. 程序化使用

#### 基本使用示例
```python
from es_client import ElasticsearchClient, ESConfig
from keyword_index_manager import KeywordIndexManager
from keyword_search_service import KeywordSearchService, SearchOptions

# 创建配置
config = ESConfig(
    hosts=["localhost:9200"],
    username="elastic",
    password="password"
)

# 创建客户端
es_client = ElasticsearchClient(config)

# 检查连接
ping_result = es_client.ping()
print(f"连接状态: {ping_result.message}")

# 创建索引管理器
index_manager = KeywordIndexManager(es_client)

# 创建索引
index_manager.create_index()

# 导入数据
index_manager.load_data_from_json("filtered_keywords-Copy3.json")

# 创建搜索服务
search_service = KeywordSearchService(es_client)

# 搜索关键词
results = search_service.search_keywords("量化", SearchOptions(size=10))
print(f"找到 {results.total} 条结果")

# 关闭连接
es_client.close()
```

#### 高级搜索示例
```python
# 多关键词搜索
results = search_service.multi_keyword_search(
    keywords=["量化", "模型"],
    operator="AND",
    options=SearchOptions(size=5, highlight=True)
)

# 相似文档搜索
similar_docs = search_service.get_similar_documents("chunk_0103_5804_5332.txt")

# 获取统计信息
stats = search_service.get_keyword_statistics()
print(f"总文档数: {stats['total_documents']}")
print(f"热门关键词: {stats['top_keywords'][:5]}")
```

## 索引设计

### 索引映射
```json
{
  "properties": {
    "chunk_id": {
      "type": "keyword",
      "fields": {
        "text": {
          "type": "text",
          "analyzer": "standard"
        }
      }
    },
    "keywords": {
      "type": "text",
      "analyzer": "ik_max_word",
      "search_analyzer": "ik_smart",
      "fields": {
        "keyword": {
          "type": "keyword"
        },
        "suggest": {
          "type": "completion"
        }
      }
    },
    "keyword_count": {
      "type": "integer"
    },
    "created_at": {
      "type": "date"
    },
    "updated_at": {
      "type": "date"
    }
  }
}
```

### 索引设置
- 分片数: 1
- 副本数: 1
- 中文分词器: ik_max_word/ik_smart
- 最大结果窗口: 50000