from es_client import ESClient  # 使用支持集群模式的ESClient

def create_keyword_index(es_client, index_name="keyword_chunks"):
    """
    在集群中创建适合关键词检索的索引映射
    优化集群环境下的分片和副本配置
    """
    # 映射结构保持不变，专注于关键词检索
    mapping = {
        "properties": {
            "filename": {
                "type": "keyword"  # 文件名精确匹配
            },
            "keywords": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword"  # 子字段用于精确匹配
                    }
                },
                "analyzer": "ik_max_word",  # 中文分词支持
                "search_analyzer": "ik_smart"
            },
            "keyword_count": {
                "type": "integer"  # 关键词数量，用于过滤和排序
            }
        }
    }
    
    # 集群环境下的索引设置优化
    settings = {
        # 集群环境分片数建议：根据节点数和数据量调整
        # 一般原则：分片数 ≤ 数据节点数 × 3
        "number_of_shards": 3,  
        # 副本数：生产环境建议1-2（提供高可用）
        "number_of_replicas": 1,
        # 刷新间隔：集群环境可适当延长减少IO压力
        "index.refresh_interval": "5s",
        # 中文分词器配置
        "analysis": {
            "analyzer": {
                "ik_max_word": {
                    "type": "ik_max_word"
                },
                "ik_smart": {
                    "type": "ik_smart"
                }
            }
        }
    }
    
    return es_client.create_index(
        index_name=index_name,
        mapping=mapping,
        settings=settings
    )

def insert_sample_data(es_client, index_name="keyword_chunks"):
    """插入示例数据到集群索引"""
    data = {
        "chunk_0125_7585_5332.txt": [
            "vllm.SamplingParams", "文本生成", "采样参数",
            "presence_penalty", "frequency_penalty", "repetition_penalty",
            "temperature", "top_p", "top_k", "束搜索"
        ],
        "chunk_0051_3274_7601.txt": [
            "环境变量", "VLLM_PORT", "VLLM_HOST_IP", "Kubernetes",
            "CUDA_VISIBLE_DEVICES", "NCCL", "FlashAttention",
            "多模态模型", "OpenVINO"
        ]
    }
    
    docs = []
    for filename, keywords in data.items():
        docs.append({
            "filename": filename,
            "keywords": keywords,
            "keyword_count": len(keywords)
        })
    
    return es_client.bulk_insert(index_name, docs)

def search_by_keyword(es_client, index_name, keyword, exact_match=False):
    """在集群中检索关键词并返回文件名"""
    if exact_match:
        # 精确匹配（适合技术术语）
        query = {"term": {"keywords.keyword": keyword}}
    else:
        # 模糊匹配（适合中文分词检索）
        query = {"match": {"keywords": keyword}}
    
    result = es_client.search(
        index_name=index_name,
        query=query,
        source=["filename"],  # 只返回需要的文件名字段
        size=100
    )
    
    if not result:
        return []
    
    return [hit["_source"]["filename"] for hit in result["hits"]["hits"]]

if __name__ == "__main__":
    # 集群节点配置（核心改动：使用多个节点地址）
    CLUSTER_HOSTS = [
        "http://es-node1:9200",
        "http://es-node2:9200",
        "http://es-node3:9200"  # 根据实际集群节点调整
    ]
    
    # 初始化集群客户端
    es = ESClient(
        hosts=CLUSTER_HOSTS,  # 集群模式使用hosts参数
        user="elastic",       # 集群认证用户名
        password="your-secure-password",  # 集群认证密码
        sniff_on_start=True,  # 启动时探测集群节点
        sniff_on_connection_fail=True,  # 连接失败时重新探测
        sniffer_timeout=60,   # 节点信息缓存时间
        timeout=30            # 操作超时时间
    )
    
    if not es.is_connected():
        print("无法连接到ES集群，退出程序")
        exit(1)
    
    # 查看集群状态（验证连接）
    cluster_health = es.get_cluster_health()
    if cluster_health:
        print(f"集群健康状态: {cluster_health['status']}")
        print(f"活跃主分片数: {cluster_health['active_primary_shards']}")
        print(f"节点数: {cluster_health['number_of_nodes']}")
    
    # 操作索引
    index_name = "keyword_chunks_cluster"
    create_keyword_index(es, index_name)
    insert_sample_data(es, index_name)
    
    # 测试检索
    print("\n--- 检索测试 ---")
    print("包含 'vllm' 的文件:")
    print(search_by_keyword(es, index_name, "vllm"))
    
    print("\n精确匹配 'Kubernetes' 的文件:")
    print(search_by_keyword(es, index_name, "Kubernetes", exact_match=True))
    
    print("\n包含 '环境' 的文件:")
    print(search_by_keyword(es, index_name, "环境"))
    
    # 清理测试数据（生产环境注释此行）
    es.delete_index(index_name)
    
    # 关闭连接
    es.close()
