2025-08-12 15:07:26 | ERROR | es_client:ping:155 | ES连接检查异常: Elasticsearch.__init__() got an unexpected keyword argument 'timeout'
2025-08-12 15:07:54 | ERROR | es_client:ping:154 | ES连接检查异常: URL must include a 'scheme', 'host', and 'port' component (ie 'https://localhost:9200')
2025-08-12 15:08:19 | INFO | es_client:client:128 | 创建ES同步客户端连接: ['http://localhost:9200']
2025-08-12 15:08:35 | ERROR | es_client:ping:160 | ES连接失败
2025-08-12 15:14:58 | INFO | es_client:client:128 | 创建ES同步客户端连接: ['http://localhost:9200']
2025-08-12 15:17:38 | ERROR | es_client:ping:201 | ES连接检查异常: Elasticsearch.__init__() got an unexpected keyword argument 'proxies'
2025-08-12 15:18:20 | WARNING | es_client:_build_client_config:153 | 检测到代理配置 *************:44444，但Elasticsearch官方客户端对代理支持有限
2025-08-12 15:18:20 | WARNING | es_client:_build_client_config:154 | 建议在系统级别配置代理或使用专门的代理工具
2025-08-12 15:18:20 | INFO | es_client:client:165 | 创建ES同步客户端连接: ['http://*************:9200', 'http://*************:9200', 'http://*************:9200']
2025-08-12 15:24:52 | INFO | es_client:client:133 | 创建ES同步客户端连接: ['http://*************:9200', 'http://*************:9200', 'http://*************:9200']
2025-08-12 15:26:16 | ERROR | es_client:ping:165 | ES连接失败
