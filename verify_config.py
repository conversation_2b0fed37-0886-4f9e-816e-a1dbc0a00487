"""
验证配置读取功能
"""

from es_client import load_es_config_from_env

def main():
    print("🔧 验证.env配置文件读取功能")
    print("=" * 50)
    
    try:
        config = load_es_config_from_env()
        
        print("✅ 成功读取配置:")
        print(f"   🔗 ES主机: {config.hosts}")
        print(f"   👤 用户名: {config.username or '未设置'}")
        print(f"   🔐 密码: {'已设置' if config.password else '未设置'}")
        print(f"   ⏱️  超时时间: {config.timeout}秒")
        print(f"   🔄 最大重试: {config.max_retries}次")
        
        if config.proxy_host:
            print(f"   🌐 代理主机: {config.proxy_host}")
            print(f"   🌐 代理端口: {config.proxy_port}")
            print(f"   🌐 代理用户: {config.proxy_username or '未设置'}")
            print(f"   🌐 代理密码: {'已设置' if config.proxy_password else '未设置'}")
        else:
            print("   🌐 代理: 未配置")
            
        if config.api_key:
            print(f"   🔑 API密钥: 已设置")
        if config.cloud_id:
            print(f"   ☁️  云ID: 已设置")
            
        print("\n🎉 配置读取功能正常！")
        print("💡 您的.env文件配置已被正确读取和解析")
        
    except Exception as e:
        print(f"❌ 配置读取失败: {str(e)}")
        print("💡 请检查.env文件是否存在且格式正确")

if __name__ == "__main__":
    main()
