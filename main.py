"""
主应用程序
演示Elasticsearch关键词索引和搜索功能
"""

import json
import asyncio
from pathlib import Path
from typing import Dict, List, Optional

from loguru import logger
from dotenv import load_dotenv
import os

from es_client import ElasticsearchClient, ESConfig
from keyword_index_manager import KeywordIndexManager
from keyword_search_service import KeywordSearchService, SearchOptions


def setup_logging():
    """设置日志"""
    # 创建logs目录
    Path("logs").mkdir(exist_ok=True)
    
    # 配置loguru
    logger.add(
        "logs/main_{time:YYYY-MM-DD}.log",
        rotation="1 day",
        retention="30 days",
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        encoding="utf-8"
    )


def load_config() -> ESConfig:
    """加载配置"""
    load_dotenv()
    
    return ESConfig(
        hosts=os.getenv("ES_HOSTS", "localhost:9200").split(","),
        username=os.getenv("ES_USERNAME"),
        password=os.getenv("ES_PASSWORD"),
        timeout=int(os.getenv("ES_TIMEOUT", "30")),
        max_retries=int(os.getenv("ES_MAX_RETRIES", "3")),
        proxy_host=os.getenv("ES_PROXY_HOST"),
        proxy_port=int(os.getenv("ES_PROXY_PORT", "0")) if os.getenv("ES_PROXY_PORT") else None,
        proxy_username=os.getenv("ES_PROXY_USERNAME"),
        proxy_password=os.getenv("ES_PROXY_PASSWORD")
    )


def print_search_results(results, title: str):
    """打印搜索结果"""
    print(f"\n{'='*50}")
    print(f"{title}")
    print(f"{'='*50}")
    print(f"总结果数: {results.total}")
    print(f"查询耗时: {results.took}ms")
    
    if results.hits:
        for i, hit in enumerate(results.hits[:5], 1):  # 只显示前5个结果
            source = hit['_source']
            score = hit['_score']
            
            print(f"\n{i}. 文档块ID: {source['chunk_id']}")
            print(f"   评分: {score:.2f}")
            print(f"   关键词数量: {source['keyword_count']}")
            print(f"   关键词: {', '.join(source['keywords'][:10])}...")  # 只显示前10个关键词
            
            # 显示高亮
            if 'highlight' in hit:
                highlights = hit['highlight'].get('keywords', [])
                if highlights:
                    print(f"   高亮: {' | '.join(highlights[:2])}")
    else:
        print("没有找到匹配的结果")


def main():
    """主函数"""
    setup_logging()
    logger.info("启动关键词搜索应用")
    
    try:
        # 加载配置
        config = load_config()
        logger.info(f"ES配置: {config.hosts}")
        
        # 创建ES客户端
        es_client = ElasticsearchClient(config)
        
        # 检查连接
        ping_result = es_client.ping()
        if not ping_result.success:
            logger.error(f"ES连接失败: {ping_result.error}")
            return
        
        logger.info("ES连接成功")
        
        # 创建索引管理器
        index_manager = KeywordIndexManager(es_client)
        
        # 创建搜索服务
        search_service = KeywordSearchService(es_client)
        
        # 检查数据文件是否存在
        data_file = "filtered_keywords-Copy3.json"
        if not Path(data_file).exists():
            logger.error(f"数据文件不存在: {data_file}")
            return
        
        print("Elasticsearch关键词搜索系统")
        print("="*50)
        
        while True:
            print("\n请选择操作:")
            print("1. 初始化索引并导入数据")
            print("2. 关键词搜索")
            print("3. 多关键词搜索")
            print("4. 文档块ID搜索")
            print("5. 相似文档搜索")
            print("6. 获取关键词统计")
            print("7. 获取关键词建议")
            print("8. 删除索引")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-8): ").strip()
            
            if choice == "0":
                break
            elif choice == "1":
                # 初始化索引并导入数据
                print("\n正在初始化索引...")
                
                # 创建索引
                create_result = index_manager.create_index()
                if create_result.success:
                    print("✓ 索引创建成功")
                    
                    # 导入数据
                    print("正在导入数据...")
                    load_result = index_manager.load_data_from_json(data_file)
                    if load_result.success:
                        print("✓ 数据导入成功")
                    else:
                        print(f"✗ 数据导入失败: {load_result.error}")
                else:
                    print(f"✗ 索引创建失败: {create_result.error}")
                    
            elif choice == "2":
                # 关键词搜索
                keyword = input("请输入要搜索的关键词: ").strip()
                if keyword:
                    options = SearchOptions(size=10, highlight=True)
                    results = search_service.search_keywords(keyword, options)
                    print_search_results(results, f"关键词搜索结果: '{keyword}'")
                    
            elif choice == "3":
                # 多关键词搜索
                keywords_input = input("请输入多个关键词(用空格分隔): ").strip()
                operator = input("请选择操作符 (AND/OR, 默认OR): ").strip().upper() or "OR"
                
                if keywords_input:
                    keywords = keywords_input.split()
                    options = SearchOptions(size=10, highlight=True)
                    results = search_service.multi_keyword_search(keywords, operator, options)
                    print_search_results(results, f"多关键词搜索结果: {keywords} ({operator})")
                    
            elif choice == "4":
                # 文档块ID搜索
                chunk_pattern = input("请输入文档块ID或模式: ").strip()
                if chunk_pattern:
                    if "*" in chunk_pattern or "?" in chunk_pattern:
                        # 模式搜索
                        options = SearchOptions(size=10)
                        results = search_service.search_by_chunk_pattern(chunk_pattern, options)
                        print_search_results(results, f"文档块模式搜索结果: '{chunk_pattern}'")
                    else:
                        # 精确搜索
                        result = index_manager.search_by_chunk_id(chunk_pattern)
                        if result.success and result.data['hits']['hits']:
                            hit = result.data['hits']['hits'][0]
                            source = hit['_source']
                            print(f"\n找到文档块: {source['chunk_id']}")
                            print(f"关键词数量: {source['keyword_count']}")
                            print(f"关键词: {', '.join(source['keywords'])}")
                        else:
                            print(f"未找到文档块: {chunk_pattern}")
                            
            elif choice == "5":
                # 相似文档搜索
                chunk_id = input("请输入文档块ID: ").strip()
                if chunk_id:
                    results = search_service.get_similar_documents(chunk_id, size=5)
                    print_search_results(results, f"相似文档搜索结果: '{chunk_id}'")
                    
            elif choice == "6":
                # 获取关键词统计
                print("\n正在获取关键词统计信息...")
                stats = search_service.get_keyword_statistics()
                
                if stats:
                    print(f"\n关键词统计信息:")
                    print(f"总文档数: {stats['total_documents']}")
                    print(f"平均关键词数: {stats['avg_keyword_count']}")
                    print(f"最大关键词数: {stats['max_keyword_count']}")
                    print(f"最小关键词数: {stats['min_keyword_count']}")
                    
                    print(f"\n热门关键词 (Top 10):")
                    for i, kw in enumerate(stats['top_keywords'][:10], 1):
                        print(f"{i:2d}. {kw['keyword']} ({kw['count']})")
                        
                    print(f"\n关键词数量分布:")
                    for bucket in stats['keyword_count_distribution'][:10]:
                        count_range = f"{int(bucket['key'])}-{int(bucket['key'])+4}"
                        print(f"  {count_range}: {bucket['doc_count']} 个文档")
                else:
                    print("获取统计信息失败")
                    
            elif choice == "7":
                # 获取关键词建议
                prefix = input("请输入关键词前缀: ").strip()
                if prefix:
                    result = index_manager.get_keyword_suggestions(prefix, size=10)
                    if result.success:
                        aggs = result.data.get('aggregations', {})
                        keywords = aggs.get('unique_keywords', {}).get('buckets', [])
                        
                        if keywords:
                            print(f"\n关键词建议 (前缀: '{prefix}'):")
                            for i, kw in enumerate(keywords, 1):
                                print(f"{i:2d}. {kw['key']} ({kw['doc_count']})")
                        else:
                            print(f"没有找到以 '{prefix}' 开头的关键词")
                    else:
                        print(f"获取关键词建议失败: {result.error}")
                        
            elif choice == "8":
                # 删除索引
                confirm = input("确认删除索引? (y/N): ").strip().lower()
                if confirm == 'y':
                    result = index_manager.delete_index()
                    if result.success:
                        print("✓ 索引删除成功")
                    else:
                        print(f"✗ 索引删除失败: {result.error}")
                        
            else:
                print("无效的选择，请重新输入")
                
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        print(f"程序运行出错: {str(e)}")
    finally:
        # 关闭ES连接
        if 'es_client' in locals():
            es_client.close()
        logger.info("程序结束")


if __name__ == "__main__":
    main()
