"""
测试Elasticsearch功能
"""

import json
from pathlib import Path

from es_client import ElasticsearchClient, ESConfig, load_es_config_from_env
from keyword_index_manager import KeywordIndexManager
from keyword_search_service import KeywordSearchService, SearchOptions


def test_basic_functionality():
    """测试基本功能"""
    print("开始测试Elasticsearch基本功能...")
    
    # 从环境变量加载配置
    print("   📋 从.env文件加载ES配置...")
    config = load_es_config_from_env()
    print(f"   🔗 ES主机: {config.hosts}")
    print(f"   👤 用户名: {config.username or '未设置'}")
    print(f"   🔐 密码: {'已设置' if config.password else '未设置'}")
    print(f"   ⏱️  超时时间: {config.timeout}秒")
    
    # 创建客户端
    es_client = ElasticsearchClient(config)
    
    # 测试连接
    print("1. 测试连接...")
    ping_result = es_client.ping()
    print(f"   连接状态: {ping_result.message}")
    
    if not ping_result.success:
        print("   ❌ ES连接失败，请检查ES服务是否启动")
        return False
    
    # 获取集群信息
    print("2. 获取集群信息...")
    info_result = es_client.get_cluster_info()
    if info_result.success:
        version = info_result.data.get('version', {}).get('number', 'unknown')
        print(f"   ✅ ES版本: {version}")
    else:
        print(f"   ❌ 获取集群信息失败: {info_result.error}")
    
    # 测试索引操作
    test_index = "test_keyword_index"
    print(f"3. 测试索引操作 ({test_index})...")
    
    # 删除可能存在的测试索引
    es_client.delete_index(test_index)
    
    # 创建索引管理器
    index_manager = KeywordIndexManager(es_client, test_index)
    
    # 创建索引
    create_result = index_manager.create_index()
    if create_result.success:
        print("   ✅ 索引创建成功")
    else:
        print(f"   ❌ 索引创建失败: {create_result.error}")
        return False
    
    # 测试文档操作
    print("4. 测试文档操作...")
    
    # 插入测试文档
    test_docs = [
        {
            "chunk_id": "test_chunk_001",
            "keywords": ["测试", "关键词", "搜索", "Elasticsearch"],
            "keyword_count": 4
        },
        {
            "chunk_id": "test_chunk_002", 
            "keywords": ["Python", "编程", "开发", "技术"],
            "keyword_count": 4
        },
        {
            "chunk_id": "test_chunk_003",
            "keywords": ["数据库", "索引", "查询", "性能"],
            "keyword_count": 4
        }
    ]
    
    bulk_result = es_client.bulk_insert(test_index, test_docs, refresh=True)
    if bulk_result.success:
        print("   ✅ 文档插入成功")
    else:
        print(f"   ❌ 文档插入失败: {bulk_result.error}")
        return False
    
    # 测试搜索功能
    print("5. 测试搜索功能...")
    
    search_service = KeywordSearchService(es_client, test_index)
    
    # 关键词搜索
    search_result = search_service.search_keywords("测试", SearchOptions(size=5))
    if search_result.total > 0:
        print(f"   ✅ 关键词搜索成功，找到 {search_result.total} 条结果")
    else:
        print("   ❌ 关键词搜索失败")
    
    # 多关键词搜索
    multi_result = search_service.multi_keyword_search(["Python", "编程"], "AND")
    if multi_result.total > 0:
        print(f"   ✅ 多关键词搜索成功，找到 {multi_result.total} 条结果")
    else:
        print("   ❌ 多关键词搜索失败")
    
    # 文档块ID搜索
    chunk_result = index_manager.search_by_chunk_id("test_chunk_001")
    if chunk_result.success and chunk_result.data['hits']['hits']:
        print("   ✅ 文档块ID搜索成功")
    else:
        print("   ❌ 文档块ID搜索失败")
    
    # 获取统计信息
    print("6. 测试统计功能...")
    stats = search_service.get_keyword_statistics()
    if stats:
        print(f"   ✅ 统计信息获取成功，总文档数: {stats['total_documents']}")
    else:
        print("   ❌ 统计信息获取失败")
    
    # 清理测试索引
    print("7. 清理测试数据...")
    delete_result = es_client.delete_index(test_index)
    if delete_result.success:
        print("   ✅ 测试索引删除成功")
    else:
        print(f"   ❌ 测试索引删除失败: {delete_result.error}")
    
    # 关闭连接
    es_client.close()
    
    print("\n✅ 所有基本功能测试完成！")
    return True


def test_data_loading():
    """测试数据加载功能"""
    print("\n开始测试数据加载功能...")
    
    # 检查数据文件
    data_file = "filtered_keywords-Copy3.json"
    if not Path(data_file).exists():
        print(f"   ❌ 数据文件不存在: {data_file}")
        return False
    
    # 读取并分析数据
    print("1. 分析数据文件...")
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        total_chunks = len(data)
        total_keywords = sum(len(keywords) for keywords in data.values())
        avg_keywords = total_keywords / total_chunks if total_chunks > 0 else 0
        
        print(f"   ✅ 数据文件分析完成:")
        print(f"      - 文档块数量: {total_chunks}")
        print(f"      - 总关键词数: {total_keywords}")
        print(f"      - 平均关键词数: {avg_keywords:.2f}")
        
        # 显示一些示例数据
        print(f"   📋 示例数据:")
        for i, (chunk_id, keywords) in enumerate(list(data.items())[:3]):
            print(f"      {i+1}. {chunk_id}: {len(keywords)} 个关键词")
            print(f"         {', '.join(keywords[:5])}...")
        
    except Exception as e:
        print(f"   ❌ 数据文件读取失败: {str(e)}")
        return False
    
    print("\n✅ 数据加载功能测试完成！")
    return True


def main():
    """主测试函数"""
    print("Elasticsearch关键词搜索系统 - 功能测试")
    print("="*60)
    
    # 测试基本功能
    basic_test_passed = test_basic_functionality()
    
    # 测试数据加载
    data_test_passed = test_data_loading()
    
    print("\n" + "="*60)
    print("测试总结:")
    print(f"基本功能测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
    print(f"数据加载测试: {'✅ 通过' if data_test_passed else '❌ 失败'}")
    
    if basic_test_passed and data_test_passed:
        print("\n🎉 所有测试通过！系统可以正常使用。")
        print("\n下一步:")
        print("1. 复制 .env.example 为 .env 并配置ES连接信息")
        print("2. 运行 python main.py 开始使用系统")
    else:
        print("\n⚠️  部分测试失败，请检查:")
        if not basic_test_passed:
            print("- Elasticsearch服务是否正常启动")
            print("- ES连接配置是否正确")
        if not data_test_passed:
            print("- filtered_keywords-Copy3.json 文件是否存在")


if __name__ == "__main__":
    main()
